import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, Al<PERSON><PERSON><PERSON>ogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { toast } from '@/hooks/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AdminAnalytics from '@/components/AdminAnalytics';

interface User {
  id: string;
  email: string;
  full_name: string;
  plan: string;
  posts_count: number;
  posts_limit: number;
  subscription_status: string;
  billing_cycle?: string;
  next_billing_date?: string;
  is_admin: number;
  created_at: string;
  total_posts_created: number;
}

interface Post {
  id: string;
  created_at: string;
  content: string;
  caption: string;
  image_url: string;
  published: number;
}

export default function AdminPanel() {
  const { user, isAdmin, checkAdminStatus } = useAuth();
  const navigate = useNavigate();
  const { userUuid } = useParams();

  console.log('AdminPanel render:', { user: user?.id, isAdmin, userUuid });

  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userPosts, setUserPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [search, setSearch] = useState('');
  const [planFilter, setPlanFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [userDetailsDialogOpen, setUserDetailsDialogOpen] = useState(false);
  const [editUserDialogOpen, setEditUserDialogOpen] = useState(false);
  const [creditsDialogOpen, setCreditsDialogOpen] = useState(false);
  const [planChangeDialogOpen, setPlanChangeDialogOpen] = useState(false);
  const [galleryDialogOpen, setGalleryDialogOpen] = useState(false);

  // Form states
  const [creditsAmount, setCreditsAmount] = useState(0);
  const [creditsAction, setCreditsAction] = useState<'add' | 'remove' | 'set'>('add');
  const [newPlan, setNewPlan] = useState('');
  const [newBillingCycle, setNewBillingCycle] = useState('');

  useEffect(() => {
    console.log('Admin Panel useEffect:', { user: user?.id, userUuid, isAdmin });

    // Check if user is logged in
    if (!user) {
      console.log('No user, redirecting to login');
      navigate('/login');
      return;
    }

    // Validate the URL format and user access
    if (!userUuid || userUuid !== user.id) {
      console.log('Invalid URL or user mismatch', { userUuid, userId: user.id });
      navigate('/');
      return;
    }

    // Check admin status from context (trust the context)
    if (!isAdmin) {
      console.log('User is not admin based on context, redirecting to home');
      navigate('/');
      return;
    }

    // If we get here, user is admin and can access the panel
    console.log('User is admin, fetching users...');
    fetchUsers();
  }, [user, userUuid, isAdmin, navigate]);

  // Separate effect for fetching users when filters change
  useEffect(() => {
    if (user && isAdmin && userUuid === user.id) {
      fetchUsers();
    }
  }, [page, search, planFilter, statusFilter]);

  const fetchUsers = async () => {
    try {
      console.log('fetchUsers: Starting...');
      const token = localStorage.getItem('auth_token');
      if (!token) {
        console.log('fetchUsers: No token, redirecting to login');
        navigate('/login');
        return;
      }

      console.log('fetchUsers: Token found, making API call...');
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...(search && { search }),
        ...(planFilter && planFilter !== 'all' && { plan: planFilter }),
        ...(statusFilter && statusFilter !== 'all' && { status: statusFilter })
      });

      console.log('fetchUsers: API URL:', `http://localhost:5001/api/admin/users?${queryParams}`);

      const response = await fetch(`http://localhost:5001/api/admin/users?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('Admin users API response:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
        setTotalPages(data.pagination.pages);
      } else if (response.status === 403) {
        const errorData = await response.json();
        console.log('403 error details:', errorData);
        toast({
          title: "Access Denied",
          description: "You don't have admin privileges.",
          variant: "destructive"
        });
        navigate('/');
      } else {
        const errorData = await response.text();
        console.log('API error:', response.status, errorData);
        throw new Error('Failed to fetch users');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchUserDetails = async (userId: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`http://localhost:5001/api/admin/users/${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSelectedUser(data.user);
        setUserPosts(data.posts);
        setUserDetailsDialogOpen(true);
      } else {
        throw new Error('Failed to fetch user details');
      }
    } catch (error) {
      console.error('Error fetching user details:', error);
      toast({
        title: "Error",
        description: "Failed to fetch user details",
        variant: "destructive"
      });
    }
  };

  const deleteUser = async (userId: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`http://localhost:5001/api/admin/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "User deleted successfully"
        });
        fetchUsers();
        setUserDetailsDialogOpen(false);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete user');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete user",
        variant: "destructive"
      });
    }
  };

  const updateCredits = async () => {
    if (!selectedUser) return;

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`http://localhost:5001/api/admin/users/${selectedUser.id}/credits`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          credits: creditsAmount,
          action: creditsAction
        })
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: "Success",
          description: `Credits updated: ${data.previousCount} → ${data.newCount}`
        });
        setCreditsDialogOpen(false);
        setCreditsAmount(0);
        fetchUsers();
        if (selectedUser) {
          fetchUserDetails(selectedUser.id);
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update credits');
      }
    } catch (error) {
      console.error('Error updating credits:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update credits",
        variant: "destructive"
      });
    }
  };

  const changePlan = async () => {
    if (!selectedUser) return;

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`http://localhost:5001/api/admin/users/${selectedUser.id}/plan`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          plan: newPlan,
          billingCycle: newBillingCycle || undefined
        })
      });

      if (response.ok) {
        const data = await response.json();
        toast({
          title: "Success",
          description: data.message
        });
        setPlanChangeDialogOpen(false);
        setNewPlan('');
        setNewBillingCycle('');
        fetchUsers();
        if (selectedUser) {
          fetchUserDetails(selectedUser.id);
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to change plan');
      }
    } catch (error) {
      console.error('Error changing plan:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to change plan",
        variant: "destructive"
      });
    }
  };

  const fetchUserGallery = async (userId: string) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`http://localhost:5001/api/admin/users/${userId}/gallery`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUserPosts(data.posts);
        setGalleryDialogOpen(true);
      } else {
        throw new Error('Failed to fetch user gallery');
      }
    } catch (error) {
      console.error('Error fetching gallery:', error);
      toast({
        title: "Error",
        description: "Failed to fetch user gallery",
        variant: "destructive"
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPlanBadgeColor = (plan: string) => {
    switch (plan) {
      case 'free': return 'bg-gray-500';
      case 'basic': return 'bg-blue-500';
      case 'pro': return 'bg-purple-500';
      case 'ultra': return 'bg-gold-500';
      default: return 'bg-gray-500';
    }
  };

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Admin Panel</CardTitle>
          <CardDescription>
            Manage users, view statistics, and handle administrative tasks.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="analytics" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="users">User Management</TabsTrigger>
            </TabsList>

            <TabsContent value="analytics" className="space-y-4">
              <AdminAnalytics />
            </TabsContent>

            <TabsContent value="users" className="space-y-4">
              {/* Filters */}
              <div className="flex gap-4 mb-6">
                <Input
                  placeholder="Search users..."
                  value={search}
                  onChange={(e) => {
                    setSearch(e.target.value);
                    setPage(1);
                  }}
                  className="max-w-sm"
                />
                <Select value={planFilter} onValueChange={(value) => {
                  setPlanFilter(value);
                  setPage(1);
                }}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by plan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All plans</SelectItem>
                    <SelectItem value="free">Free</SelectItem>
                    <SelectItem value="basic">Basic</SelectItem>
                    <SelectItem value="pro">Pro</SelectItem>
                    <SelectItem value="ultra">Ultra</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={(value) => {
                  setStatusFilter(value);
                  setPage(1);
                }}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="expired">Expired</SelectItem>
                  </SelectContent>
                </Select>
              </div>

          {/* Users Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Plan</TableHead>
                <TableHead>Posts</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{user.full_name}</div>
                      <div className="text-sm text-muted-foreground">{user.email}</div>
                      {user.is_admin === 1 && (
                        <Badge variant="secondary" className="text-xs">Admin</Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getPlanBadgeColor(user.plan)}>
                      {user.plan.toUpperCase()}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {user.posts_count}/{user.posts_limit}
                    <div className="text-sm text-muted-foreground">
                      {user.total_posts_created} total
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={user.subscription_status === 'active' ? 'default' : 'secondary'}>
                      {user.subscription_status}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(user.created_at)}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fetchUserDetails(user.id)}
                      >
                        View
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fetchUserGallery(user.id)}
                      >
                        Gallery
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Pagination */}
          <div className="flex justify-between items-center mt-6">
            <div className="text-sm text-muted-foreground">
              Page {page} of {totalPages}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                disabled={page <= 1}
                onClick={() => setPage(page - 1)}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={page >= totalPages}
                onClick={() => setPage(page + 1)}
              >
                Next
              </Button>
              </div>
            </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* User Details Dialog */}
      <Dialog open={userDetailsDialogOpen} onOpenChange={setUserDetailsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>User Details</DialogTitle>
            <DialogDescription>
              Manage user account and settings
            </DialogDescription>
          </DialogHeader>

          {selectedUser && (
            <Tabs defaultValue="info" className="w-full">
              <TabsList>
                <TabsTrigger value="info">Information</TabsTrigger>
                <TabsTrigger value="posts">Posts ({userPosts.length})</TabsTrigger>
                <TabsTrigger value="actions">Actions</TabsTrigger>
              </TabsList>

              <TabsContent value="info" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Name</Label>
                    <div className="font-medium">{selectedUser.full_name}</div>
                  </div>
                  <div>
                    <Label>Email</Label>
                    <div className="font-medium">{selectedUser.email}</div>
                  </div>
                  <div>
                    <Label>Plan</Label>
                    <Badge className={getPlanBadgeColor(selectedUser.plan)}>
                      {selectedUser.plan.toUpperCase()}
                    </Badge>
                  </div>
                  <div>
                    <Label>Posts Used</Label>
                    <div>{selectedUser.posts_count}/{selectedUser.posts_limit}</div>
                  </div>
                  <div>
                    <Label>Subscription Status</Label>
                    <Badge variant={selectedUser.subscription_status === 'active' ? 'default' : 'secondary'}>
                      {selectedUser.subscription_status}
                    </Badge>
                  </div>
                  <div>
                    <Label>Billing Cycle</Label>
                    <div>{selectedUser.billing_cycle || 'N/A'}</div>
                  </div>
                  <div>
                    <Label>Next Billing</Label>
                    <div>{selectedUser.next_billing_date ? formatDate(selectedUser.next_billing_date) : 'N/A'}</div>
                  </div>
                  <div>
                    <Label>Admin Status</Label>
                    <Badge variant={selectedUser.is_admin ? 'default' : 'secondary'}>
                      {selectedUser.is_admin ? 'Admin' : 'User'}
                    </Badge>
                  </div>
                  <div>
                    <Label>Created</Label>
                    <div>{formatDate(selectedUser.created_at)}</div>
                  </div>
                  <div>
                    <Label>Total Posts Created</Label>
                    <div>{selectedUser.total_posts_created}</div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="posts" className="space-y-4">
                <div className="grid gap-4 max-h-96 overflow-y-auto">
                  {userPosts.map((post) => (
                    <Card key={post.id}>
                      <CardContent className="p-4">
                        <div className="flex gap-4">
                          {post.image_url && (
                            <img
                              src={post.image_url}
                              alt="Post"
                              className="w-20 h-20 object-cover rounded"
                            />
                          )}
                          <div className="flex-1">
                            <div className="text-sm font-medium">
                              {formatDate(post.created_at)}
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              {post.caption?.substring(0, 100)}...
                            </div>
                            <Badge variant={post.published ? 'default' : 'secondary'} className="mt-2">
                              {post.published ? 'Published' : 'Draft'}
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="actions" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Button
                    onClick={() => {
                      setCreditsDialogOpen(true);
                      setCreditsAmount(0);
                      setCreditsAction('add');
                    }}
                  >
                    Manage Credits
                  </Button>
                  <Button
                    onClick={() => {
                      setPlanChangeDialogOpen(true);
                      setNewPlan(selectedUser.plan);
                      setNewBillingCycle(selectedUser.billing_cycle || '');
                    }}
                  >
                    Change Plan
                  </Button>
                </div>

                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" className="w-full">
                      Delete User Account
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete the user account
                        and all associated data including posts and images.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => deleteUser(selectedUser.id)}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      >
                        Delete Account
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </TabsContent>
            </Tabs>
          )}
        </DialogContent>
      </Dialog>

      {/* Credits Management Dialog */}
      <Dialog open={creditsDialogOpen} onOpenChange={setCreditsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Manage User Credits</DialogTitle>
            <DialogDescription>
              Add, remove, or set the number of posts for this user.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label>Action</Label>
              <Select value={creditsAction} onValueChange={(value: 'add' | 'remove' | 'set') => setCreditsAction(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="add">Add Credits</SelectItem>
                  <SelectItem value="remove">Remove Credits</SelectItem>
                  <SelectItem value="set">Set Credits</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Amount</Label>
              <Input
                type="number"
                value={creditsAmount}
                onChange={(e) => setCreditsAmount(parseInt(e.target.value) || 0)}
                min="0"
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setCreditsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={updateCredits}>
              Update Credits
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Plan Change Dialog */}
      <Dialog open={planChangeDialogOpen} onOpenChange={setPlanChangeDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change User Plan</DialogTitle>
            <DialogDescription>
              Update the user's subscription plan and billing cycle.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label>Plan</Label>
              <Select value={newPlan} onValueChange={setNewPlan}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="free">Free</SelectItem>
                  <SelectItem value="basic">Basic</SelectItem>
                  <SelectItem value="pro">Pro</SelectItem>
                  <SelectItem value="ultra">Ultra</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {newPlan !== 'free' && (
              <div>
                <Label>Billing Cycle</Label>
                <Select value={newBillingCycle} onValueChange={setNewBillingCycle}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="yearly">Yearly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setPlanChangeDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={changePlan}>
              Change Plan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Gallery Dialog */}
      <Dialog open={galleryDialogOpen} onOpenChange={setGalleryDialogOpen}>
        <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>User Gallery</DialogTitle>
            <DialogDescription>
              View all posts created by this user
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {userPosts.map((post) => (
              <Card key={post.id}>
                <CardContent className="p-4">
                  {post.image_url && (
                    <img
                      src={post.image_url}
                      alt="Post"
                      className="w-full h-48 object-cover rounded mb-3"
                    />
                  )}
                  <div className="space-y-2">
                    <div className="text-sm font-medium">
                      {formatDate(post.created_at)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {post.caption?.substring(0, 100)}...
                    </div>
                    <Badge variant={post.published ? 'default' : 'secondary'}>
                      {post.published ? 'Published' : 'Draft'}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {userPosts.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No posts found for this user.
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}