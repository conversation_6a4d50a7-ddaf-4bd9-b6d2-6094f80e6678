import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import dotenv from 'dotenv';
import { GoogleGenAI, Modality } from '@google/genai';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { testConnection, initDatabase, getOnlineUsersCount, trackAnalyticsEvent, trackUserSession, updateUserActivity, removeUserSession, getOne, getAll } from './sqlite-db.js';
import {
  registerUser,
  loginUser,
  getUserProfile,
  updateUserProfile,
  verifyToken
} from './sqlite-auth.js';
import { verifyPayPalPayment, getPlanDetails, getNextBillingDate, getSubscriptionPlanDetails, getAccessToken as getPayPalAccessToken, verifyWebhookSignature } from './paypal.js';
import { handleWebhookEvent } from './webhook-handlers.js';
import { runSubscriptionMaintenance } from './subscription-sync.js';
import { authenticateAdmin, checkAdminStatus } from './admin-middleware.js';
import {
  validateBody,
  validateParams,
  registerSchema,
  loginSchema,
  generatePostSchema,
  generateImageSchema,
  updateProfileSchema,
  contactSchema,
  sanitizeObject,
  uuidSchema
} from './validation.js';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create images directory if it doesn't exist
const imagesDir = path.join(__dirname, '..', 'data', 'images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
  console.log('Created images directory at:', imagesDir);
}

// Load environment variables
dotenv.config();

// Function to reload environment variables
const reloadEnvVars = () => {
  try {
    const envPath = path.join(__dirname, '..', '.env');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = dotenv.parse(envContent);

    // Update process.env with the new values
    Object.keys(envVars).forEach(key => {
      process.env[key] = envVars[key];
    });

    console.log('Environment variables reloaded successfully');
  } catch (error) {
    console.error('Error reloading environment variables:', error);
  }
};

// Reload environment variables to ensure they're up to date
reloadEnvVars();


// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
    },
  },
  crossOriginEmbedderPolicy: false // Allow embedding for PayPal
}));

app.use(cors());

// Increase JSON payload size limit to 50MB to handle large base64 images
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));

// Sanitization middleware
app.use((req, res, next) => {
  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  next();
});

// Helper function to get client IP
const getClientIP = (req) => {
  const ip = req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
             req.headers['x-real-ip'] ||
             req.connection?.remoteAddress ||
             req.socket?.remoteAddress ||
             req.ip ||
             '0.0.0.0';

  // Normalize localhost IPs to a consistent format
  if (ip === '::1' || ip === '::ffff:127.0.0.1' || ip === '127.0.0.1') {
    return 'localhost';
  }

  return ip;
};

// Check if IP already has an account (limit 1 account per IP)
const checkIPLimit = async (req, res, next) => {
  try {
    const clientIP = getClientIP(req);

    // Skip IP limiting for localhost in development mode
    if (clientIP === 'localhost' && process.env.NODE_ENV !== 'production') {
      console.log('Development mode: Skipping IP limit check for localhost');
      req.clientIP = clientIP;
      return next();
    }

    // Check if this IP already registered an account by looking at user_sessions
    // We need to check for all possible formats of the same IP due to historical inconsistency
    const ipVariants = [clientIP];
    if (clientIP === 'localhost') {
      ipVariants.push('::1', '127.0.0.1', '127.0.0.1 (localhost)');
    }

    const existingUser = getOne(
      `SELECT DISTINCT us.user_id, u.email, u.created_at
       FROM user_sessions us
       JOIN users u ON us.user_id = u.id
       WHERE us.ip_address IN (${ipVariants.map(() => '?').join(',')})
       ORDER BY u.created_at ASC
       LIMIT 1`,
      ipVariants
    );

    if (existingUser) {
      return res.status(429).json({
        error: 'Account limit reached',
        message: 'Only one account is allowed per IP address for security reasons. An account was already created from this IP address.',
        existingUserEmail: existingUser.email,
        existingUserCreated: existingUser.created_at
      });
    }

    req.clientIP = clientIP;
    next();
  } catch (error) {
    console.error('IP limit check error:', error);
    next(); // Continue if check fails to avoid blocking legitimate users
  }
};

// Authentication middleware
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Authentication token required' });
  }

  const user = verifyToken(token);
  if (!user) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }

  // Update user activity
  try {
    await updateUserActivity(token);
  } catch (activityError) {
    console.error('Error updating user activity:', activityError);
    // Don't fail the request if activity update fails
  }

  req.user = user;
  next();
};

// Optional authentication middleware for contact form
const optionalAuth = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token) {
    const user = verifyToken(token);
    if (user) {
      req.user = user;
    }
  }

  next();
};

// Get all available API keys from environment variables
const getAvailableApiKeys = () => {
  const keys = [];

  // Add main API key
  if (process.env.GOOGLE_API_KEY) {
    keys.push(process.env.GOOGLE_API_KEY);
  }

  // Add fallback keys (GOOGLE_API_KEY_1, GOOGLE_API_KEY_2, etc.)
  let i = 1;
  while (process.env[`GOOGLE_API_KEY_${i}`]) {
    keys.push(process.env[`GOOGLE_API_KEY_${i}`]);
    i++;
  }

  return keys;
};

// Initialize Google GenAI with fallback support
const getAiModel = (quality = 'balanced', apiKeyIndex = 0) => {
  const availableKeys = getAvailableApiKeys();

  if (availableKeys.length === 0) {
    throw new Error('No Google API keys found in environment variables');
  }

  if (apiKeyIndex >= availableKeys.length) {
    throw new Error('All available API keys have been exhausted');
  }

  const apiKey = availableKeys[apiKeyIndex];
  const ai = new GoogleGenAI({ apiKey });

  // Select model based on quality parameter
  let modelName;
  switch (quality.toLowerCase()) {
    case 'quality':
      modelName = 'gemini-2.5-flash-preview-04-17';
      break;
    case 'fast':
      modelName = 'gemini-2.0-flash-lite';
      break;
    case 'balanced':
    default:
      modelName = 'gemma-3n-e4b-it';
      break;
  }

  return { ai, modelName, apiKeyIndex, totalKeys: availableKeys.length };
};

// Helper function to attempt API call with fallback
const attemptApiCallWithFallback = async (apiCall, quality = 'balanced') => {
  const availableKeys = getAvailableApiKeys();
  let lastError = null;

  for (let i = 0; i < availableKeys.length; i++) {
    try {
      console.log(`Attempting API call with key ${i + 1}/${availableKeys.length}`);
      const { ai, modelName } = getAiModel(quality, i);
      const result = await apiCall(ai, modelName);
      console.log(`API call successful with key ${i + 1}`);
      return result;
    } catch (error) {
      console.error(`API call failed with key ${i + 1}:`, error.message);
      lastError = error;

      // Check if this is an API key related error that should trigger fallback
      const errorString = error.message || error.toString();
      const isApiKeyError = errorString.includes('API_KEY_INVALID') ||
                           errorString.includes('PERMISSION_DENIED') ||
                           errorString.includes('RESOURCE_EXHAUSTED') ||
                           errorString.includes('QUOTA_EXCEEDED') ||
                           errorString.includes('INVALID_ARGUMENT') ||
                           errorString.includes('API key expired') ||
                           errorString.includes('API key invalid') ||
                           errorString.includes('quota exceeded');

      if (!isApiKeyError && i === 0) {
        // If it's not an API key error, don't try other keys
        throw error;
      }
    }
  }

  // All keys failed
  throw new Error(`All API keys failed. Last error: ${lastError?.message || 'Unknown error'}`);
};

// API Routes
// Generate post content API endpoint
app.post('/api/generate-post', validateBody(generatePostSchema), async (req, res) => {
  try {
    // Get user from token if available
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    let userId = null;
    let profile = null;
    let hasUnlimitedAccess = false;

    if (token) {
      const user = verifyToken(token);
      if (user) {
        userId = user.sub;
        const profileResult = await getUserProfile(userId);
        if (!profileResult.error) {
          profile = profileResult.data;

          // Check if user has unlimited posts
          hasUnlimitedAccess = await hasUnlimitedPosts(userId);

          // Check if user has reached post limit (if they don't have unlimited access)
          if (!hasUnlimitedAccess && profile.posts_count >= profile.posts_limit) {
            return res.status(403).json({
              error: `You've reached your limit of ${profile.posts_limit} posts for your ${profile.plan} plan this month.`,
              limitReached: true
            });
          }

          // Check rate limiting if we have a valid profile
          if (profile && profile.last_post_time) {
            const now = new Date();
            const lastPostTime = new Date(profile.last_post_time);
            const rateLimit = getRateLimit(profile.plan);
            const timeDiffSeconds = (now - lastPostTime) / 1000;
            const requiredWaitTimeSeconds = 60 / rateLimit;

            if (timeDiffSeconds < requiredWaitTimeSeconds) {
              const waitTimeLeft = Math.ceil(requiredWaitTimeSeconds - timeDiffSeconds);
              return res.status(429).json({
                error: `Rate limit exceeded. Please wait ${waitTimeLeft} seconds before generating another post.`,
                waitTimeLeft
              });
            }
          }
        } else {
          return res.status(404).json({ error: 'User profile not found' });
        }
      } else {
        return res.status(401).json({ error: 'Invalid authentication token' });
      }
    } else {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const {
      platform = 'Instagram',
      tone = 'friendly',
      topic = 'spring gardening tips',
      hashtagCount = 5,
      imageSize = '4:5',
      quality = 'balanced',
      postId = null // Allow passing a post ID to update an existing post
    } = req.body;

    // Log if we're updating an existing post
    if (postId) {
      console.log(`Generating content for existing post ${postId}`);
    }

    // Check if FREE user is trying to use Quality mode
    if (profile && profile.plan === 'free' && quality.toLowerCase() === 'quality') {
      return res.status(403).json({
        error: 'Quality mode is not available on the FREE plan. Please upgrade to use this feature.',
        qualityRestriction: true
      });
    }

    // Convert "Auto" to a specific aspect ratio for image generation
    const actualImageSize = imageSize === "Auto" ? "1:1" : imageSize;

    // Build the dynamic prompt
    const userPrompt = `
Generate a social media post for ${platform} with a ${tone} tone about: "${topic}"

Your response should include:
1. A caption that follows ${platform}'s best practices and character limits
2. ${hashtagCount > 0 ? `${hashtagCount} relevant hashtags` : 'No hashtags'}
3. An image prompt that would work well for ${actualImageSize} aspect ratio

IMPORTANT: In the image prompt, explicitly include the dimensions/aspect ratio "${actualImageSize}" so the image generation AI knows the correct size to use.

Format your response as:
CAPTION: [The social media caption text]
HASHTAGS: [The hashtags, if requested]
IMAGE_PROMPT: [A detailed prompt for generating an image that matches the caption, including "${actualImageSize}" aspect ratio]
    `.trim();

    console.log('Generating post content with fallback API keys...');
    console.log('Prompt:', userPrompt);

    // Use fallback system for text generation
    const result = await attemptApiCallWithFallback(async (ai, modelName) => {
      console.log('Sending request to Gemini API with model:', modelName);
      return await ai.models.generateContent({
        model: modelName,
        contents: userPrompt,
      });
    }, quality);

    console.log('Received response from Gemini API');
    // In the new API, the response structure is different
    console.log('Response structure:', Object.keys(result));
    console.log('Full response:', JSON.stringify(result, null, 2));

    // Get the text from the response
    let text;
    if (result.candidates && result.candidates[0] && result.candidates[0].content) {
      // New API structure
      text = result.candidates[0].content.parts[0].text;
    } else if (result.response && typeof result.response.text === 'function') {
      // Old API structure
      text = result.response.text();
    } else if (typeof result.text === 'function') {
      // Another possible structure
      text = result.text();
    } else if (typeof result.text === 'string') {
      // Direct text property
      text = result.text;
    } else {
      // Fallback
      throw new Error('Unable to extract text from API response');
    }

    console.log('Raw response text:', text);

    // Parse the response into structured format
    const captionMatch = text.match(/CAPTION:(.*?)(?=HASHTAGS:|IMAGE_PROMPT:|$)/s);
    const hashtagsMatch = text.match(/HASHTAGS:(.*?)(?=IMAGE_PROMPT:|$)/s);
    const imagePromptMatch = text.match(/IMAGE_PROMPT:(.*?)(?=$)/s);

    console.log('Parsed matches:', {
      captionMatch: captionMatch ? 'found' : 'not found',
      hashtagsMatch: hashtagsMatch ? 'found' : 'not found',
      imagePromptMatch: imagePromptMatch ? 'found' : 'not found'
    });

    // Format the response to match the expected structure
    const structuredResponse = {
      CAPTION: captionMatch ? captionMatch[1].trim() : '',
      HASHTAGS: hashtagsMatch ? hashtagsMatch[1].trim() : '',
      IMAGE_PROMPT: imagePromptMatch ? imagePromptMatch[1].trim() : '',
    };

    console.log('Structured response:', structuredResponse);

    // Update last_post_time if we have a valid profile
    if (userId && profile) {
      try {
        await updateUserProfile(userId, {
          last_post_time: new Date().toISOString()
        });
        console.log('Updated last_post_time for user:', userId);
      } catch (err) {
        console.error('Error updating last_post_time:', err);
      }
    }

    res.json(structuredResponse);
  } catch (error) {
    console.error('Error generating post:', error);

    // If all API keys failed, return the user's original content with an error message
    if (error.message && error.message.includes('All API keys failed')) {
      return res.status(500).json({
        error: 'All API keys have failed or reached their limits. Please try again later.',
        originalContent: {
          platform: platform || 'Unknown',
          tone: tone || 'Unknown',
          topic: topic || 'Unknown',
          hashtagCount: hashtagCount || 0,
          imageSize: imageSize || 'Auto'
        },
        allKeysFailed: true
      });
    }

    res.status(500).json({ error: error.message || 'Failed to generate post' });
  }
});

// Image generation endpoint
app.post('/api/generate-image', validateBody(generateImageSchema), async (req, res) => {
  try {
    const { imagePrompt, postContent, caption, userId } = req.body;

    if (!imagePrompt) {
      return res.status(400).json({ error: 'Image prompt is required' });
    }

    // Check if user has reached post limit
    if (userId) {
      const user = verifyToken(userId);
      if (user) {
        const actualUserId = user.sub;

        // Check if user has unlimited posts
        const hasUnlimitedAccess = await hasUnlimitedPosts(actualUserId);

        if (!hasUnlimitedAccess) {
          // Check if user has reached post limit
          const profileResult = await getUserProfile(actualUserId);

          if (!profileResult.error) {
            const profile = profileResult.data;

            if (profile.posts_count >= profile.posts_limit) {
              return res.status(403).json({
                error: `You've reached your limit of ${profile.posts_limit} posts for your ${profile.plan} plan this month.`,
                limitReached: true
              });
            }
          }
        }
      }
    }

    console.log('Generating image with prompt:', imagePrompt);

    // Use fallback system for image generation
    const response = await attemptApiCallWithFallback(async (ai, modelName) => {
      console.log('Sending image generation request to Gemini API');
      return await ai.models.generateContent({
        model: "gemini-2.0-flash-preview-image-generation",
        contents: imagePrompt,
        config: {
          responseModalities: [Modality.IMAGE, Modality.TEXT],
        },
      });
    }, 'balanced'); // Use balanced quality for image generation

    console.log('Image generation response structure:', Object.keys(response));

    // Log the full response for debugging
    console.log('Full response structure:', JSON.stringify(response, null, 2));

    // Check if we have candidates
    if (!response.candidates || response.candidates.length === 0) {
      throw new Error('No candidates returned from the API');
    }

    // Check if we have content
    if (!response.candidates[0].content || !response.candidates[0].content.parts) {
      throw new Error('No content parts found in the response');
    }

    // Extract the image data
    const parts = response.candidates[0].content.parts;
    console.log('Content parts:', parts.map(p => p.inlineData ? 'inlineData' : p.text ? 'text' : 'unknown'));

    const part = parts.find(p => p.inlineData);
    if (!part || !part.inlineData) {
      throw new Error('No image data returned in the response');
    }

    const imageData = part.inlineData.data;
    const mimeType = part.inlineData.mimeType;

    // Generate a unique ID for the image
    const imageId = uuidv4();
    const fileExtension = mimeType.split('/')[1] || 'jpeg';
    const fileName = `${imageId}.${fileExtension}`;
    const filePath = path.join(imagesDir, fileName);

    // Extract postId from the request for logging
    const requestPostId = req.body.postId;
    console.log('Image generation request received with postId:', requestPostId || 'none');

    // Save the image to the filesystem
    try {
      fs.writeFileSync(filePath, Buffer.from(imageData, 'base64'));
      console.log(`Image saved to ${filePath}`);

      // If we have user ID and post content, automatically save the post
      let postResult = null;
      if (userId && postContent) {
        // Verify the token
        const user = verifyToken(userId);
        if (user) {
          const actualUserId = user.sub;

          // Parse the post content
          let parsedContent;
          try {
            parsedContent = JSON.parse(postContent);
          } catch (err) {
            console.error('Error parsing post content:', err);
            parsedContent = { error: 'Invalid JSON' };
          }

          // Check if we have a postId - if so, we'll update an existing post
          const postId = parsedContent.postId;

          // Import the prevent-duplicate-posts module
          const { preventDuplicatePost } = await import('./prevent-duplicate-posts.js');

          // Check if we should update an existing post
          const { shouldUpdate, postId: finalPostId } = await preventDuplicatePost(actualUserId, postId);

          if (finalPostId) {
            console.log(`Updating existing post ${finalPostId} with new image for user ${actualUserId}`);
            // Update the parsedContent with the final post ID
            parsedContent.postId = finalPostId;
          } else {
            console.log(`Creating new post for user ${actualUserId}`);
          }

          // Ensure caption is properly formatted
          let finalCaption = caption;

          // If caption is null or undefined, create an empty string
          if (!finalCaption) {
            finalCaption = '';
            console.log('No caption provided, using empty string');
          } else {
            console.log('Using provided caption:', finalCaption.substring(0, 100) + (finalCaption.length > 100 ? '...' : ''));
          }

          // Save or update the post with the image path
          const postIdToUse = finalPostId || postId;
          console.log(`Using post ID for saving: ${postIdToUse || 'none (creating new post)'}`);

          postResult = await savePost(
            actualUserId,
            postContent,
            finalCaption,
            `/api/images/${imageId}`,
            postIdToUse // Use the final post ID from our duplicate check
          );

          if (postResult.error) {
            console.error('Error auto-saving post:', postResult.error);
          } else {
            console.log('Post auto-saved successfully with ID:', postResult.postId);

            // If this is a new post (no postId provided), copy the image to a more predictable filename
            if (!postId && postResult.postId) {
              try {
                // Create a copy of the image with the post ID in the filename for easier reference
                const newFileName = `post_${postResult.postId}.${fileExtension}`;
                const newFilePath = path.join(imagesDir, newFileName);

                // Copy the file
                fs.copyFileSync(filePath, newFilePath);
                console.log(`Created additional copy of image with post ID in filename: ${newFilePath}`);
              } catch (copyErr) {
                console.error('Error creating additional copy of image:', copyErr);
                // This is not critical, so we'll continue even if it fails
              }
            }
          }
        }
      }

      // Return both the image path and the base64 data (for immediate display)
      res.json({
        imageId: imageId,
        imagePath: `/api/images/${imageId}`,
        imageData: `data:${mimeType};base64,${imageData}`,
        postResult: postResult
      });
    } catch (err) {
      console.error('Error saving image to filesystem:', err);
      // Fall back to just returning the base64 data
      res.json({
        imageData: `data:${mimeType};base64,${imageData}`
      });
    }

  } catch (error) {
    console.error('Error generating image:', error);

    // If all API keys failed, return the user's original content with an error message
    if (error.message && error.message.includes('All API keys failed')) {
      return res.status(500).json({
        error: 'All API keys have failed or reached their limits. Please try again later.',
        originalContent: {
          imagePrompt,
          postContent,
          caption
        },
        allKeysFailed: true
      });
    }

    // Extract more detailed error information if available
    let errorMessage = 'Failed to generate image';
    if (error.message) {
      errorMessage = error.message;
    }

    // Check if there's a more detailed error in the cause
    if (error.cause) {
      console.error('Error cause:', error.cause);
      errorMessage += ' - ' + error.cause;
    }

    res.status(500).json({
      error: errorMessage,
      details: error.toString(),
      stack: error.stack
    });
  }
});



// Authentication routes
app.post('/api/auth/register',
  checkIPLimit,
  validateBody(registerSchema),
  async (req, res) => {
    try {
      const { email, password, fullName } = req.body;
      const clientIP = req.clientIP;

      const result = await registerUser(email, password, fullName);

      if (result.error) {
        return res.status(400).json({ error: result.error.message });
      }

      // Track user session with IP for the IP limit system
      try {
        const userId = result.data.user.id;
        const sessionToken = result.data.token;
        const userAgent = req.get('User-Agent') || 'unknown';

        await trackUserSession(userId, sessionToken, clientIP, userAgent);
        console.log(`Registration session tracked for user ${userId} from IP ${clientIP}`);
      } catch (sessionError) {
        console.error('Error tracking registration session:', sessionError);
      }

      res.status(201).json(result.data);
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({ error: 'Registration failed' });
    }
  }
);

app.post('/api/auth/login',
  validateBody(loginSchema),
  async (req, res) => {
    try {
      const { email, password } = req.body;

      const result = await loginUser(email, password);

      if (result.error) {
        return res.status(401).json({ error: result.error.message });
      }

      // Track user session
      try {
        const userId = result.data.user.id;
        const sessionToken = result.data.token;
        const ipAddress = getClientIP(req);
        const userAgent = req.get('User-Agent') || 'unknown';

        await trackUserSession(userId, sessionToken, ipAddress, userAgent);
        console.log(`Session tracked for user ${userId} from IP ${ipAddress}`);
      } catch (sessionError) {
        console.error('Error tracking user session:', sessionError);
        // Don't fail login if session tracking fails
      }

    res.json(result.data);
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// User profile routes
app.get('/api/profile', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;
    const result = await getUserProfile(userId);

    if (result.error) {
      return res.status(404).json({ error: result.error.message });
    }

    res.json(result.data);
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ error: 'Failed to get profile' });
  }
});

// Force refresh profile endpoint
app.get('/api/profile/refresh', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;

    // Force a fresh database query by bypassing any caching
    const result = await getUserProfile(userId, true);

    if (result.error) {
      return res.status(404).json({ error: result.error.message });
    }

    res.json(result.data);
  } catch (error) {
    console.error('Refresh profile error:', error);
    res.status(500).json({ error: 'Failed to refresh profile' });
  }
});

app.patch('/api/profile', authenticateToken, validateBody(updateProfileSchema), async (req, res) => {
  try {
    const userId = req.user.sub;
    const updates = req.body;

    const result = await updateUserProfile(userId, updates);

    if (result.error) {
      return res.status(400).json({ error: result.error.message });
    }

    res.json(result.data);
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// Contact form endpoint
console.log('Registering /api/contact endpoint');
app.post('/api/contact', optionalAuth, validateBody(contactSchema), async (req, res) => {
  try {
    const { name, email, message } = req.body;
    const timestamp = new Date().toISOString();
    const messageId = uuidv4();
    const clientIP = getClientIP(req);
    const userId = req.user ? req.user.sub : null;

    // Create message object
    const contactMessage = {
      id: messageId,
      name,
      email,
      message,
      timestamp,
      ip: clientIP,
      userId
    };

    // Create messages directory if it doesn't exist
    const messagesDir = path.join(__dirname, '..', 'data', 'messages');
    if (!fs.existsSync(messagesDir)) {
      fs.mkdirSync(messagesDir, { recursive: true });
    }

    // Save to text file
    const messageText = `
=== MESSAGE ${messageId} ===
Date: ${timestamp}
Name: ${name}
Email: ${email}
IP: ${clientIP}
User ID: ${userId || 'Guest'}
Message:
${message}
==============================

`;

    const filename = `messages_${new Date().toISOString().split('T')[0]}.txt`;
    const filepath = path.join(messagesDir, filename);

    fs.appendFileSync(filepath, messageText);

    res.json({ success: true, messageId });
  } catch (error) {
    console.error('Contact form error:', error);
    res.status(500).json({ error: 'Failed to save message' });
  }
});

// PayPal payment routes
app.post('/api/payments/paypal/capture', authenticateToken, async (req, res) => {
  try {
    console.log('PayPal capture endpoint called');
    const userId = req.user.sub;
    const { orderID, planName, billingCycle, paymentDetails } = req.body;

    console.log('Capture request details:', {
      userId,
      orderID,
      planName,
      billingCycle,
      paymentStatus: paymentDetails?.status
    });

    // Skip PayPal verification for now since we already captured on frontend
    // In production, you might want to verify with PayPal, but for testing let's skip this
    console.log('Using payment details from frontend capture');

    // Check if payment was completed (from frontend capture)
    if (paymentDetails?.status !== 'COMPLETED') {
      console.log('Payment not completed, status:', paymentDetails?.status);
      return res.status(400).json({ error: 'Payment not completed' });
    }

    // Get plan details
    console.log('Getting plan details for:', planName, billingCycle);
    const planDetails = getPlanDetails(planName, billingCycle);
    console.log('Plan details:', planDetails);

    // Verify payment amount matches plan price
    const paidAmount = parseFloat(paymentDetails.purchase_units[0].amount.value);
    console.log('Paid amount:', paidAmount, 'Expected:', planDetails.price);

    if (Math.abs(paidAmount - planDetails.price) > 0.01) { // Allow small floating point differences
      console.log('Payment amount mismatch');
      return res.status(400).json({ error: 'Payment amount mismatch' });
    }

    // Calculate next billing date
    const nextBillingDate = getNextBillingDate(billingCycle);
    console.log('Next billing date:', nextBillingDate);

    // Update user's plan
    const updates = {
      plan: planName.toLowerCase(),
      posts_limit: planDetails.posts_limit,
      posts_count: 0, // Reset post count for new billing cycle
      subscription_status: 'active',
      billing_cycle: billingCycle,
      next_billing_date: nextBillingDate.toISOString(),
      paypal_order_id: orderID,
      updated_at: new Date().toISOString()
    };

    console.log('Updating user profile with:', updates);
    const result = await updateUserProfile(userId, updates);

    if (result.error) {
      console.error('Failed to update user profile:', result.error);
      return res.status(500).json({ error: 'Failed to update user plan' });
    }

    // Log the successful payment
    console.log(`Payment successful: User ${userId} upgraded to ${planName} plan`);

    res.json({
      success: true,
      message: `Successfully upgraded to ${planName} plan`,
      plan: updates
    });

  } catch (error) {
    console.error('PayPal capture error:', error);
    res.status(500).json({
      error: 'Payment processing failed',
      details: error.message
    });
  }
});

// Get PayPal configuration for frontend
app.get('/api/payments/paypal/config', (req, res) => {
  try {
    const config = {
      clientId: process.env.PAYPAL_CLIENT_ID,
      environment: process.env.PAYPAL_ENVIRONMENT || 'sandbox'
    };

    if (!config.clientId) {
      console.log('PayPal Client ID not configured in environment variables');
      return res.status(400).json({
        error: 'PayPal not configured',
        message: 'Please add PAYPAL_CLIENT_ID to your .env file'
      });
    }

    res.json(config);
  } catch (error) {
    console.error('PayPal config error:', error);
    res.status(500).json({
      error: 'PayPal configuration error',
      message: error.message
    });
  }
});

// PayPal subscription endpoints
app.post('/api/payments/paypal/subscription-config', authenticateToken, async (req, res) => {
  try {
    const { planName, billingCycle } = req.body;

    console.log('Getting subscription config for:', planName, billingCycle);

    const subscriptionDetails = getSubscriptionPlanDetails(planName, billingCycle);
    console.log('Subscription details:', subscriptionDetails);

    res.json(subscriptionDetails);
  } catch (error) {
    console.error('Subscription config error:', error);
    res.status(400).json({
      error: 'Subscription configuration error',
      message: error.message
    });
  }
});

app.post('/api/payments/paypal/subscription-activate', authenticateToken, async (req, res) => {
  try {
    console.log('PayPal subscription activation endpoint called');
    const userId = req.user.sub;
    const { subscriptionID, planName, billingCycle } = req.body;

    console.log('Subscription activation details:', {
      userId,
      subscriptionID,
      planName,
      billingCycle
    });

    // Get subscription plan details
    const subscriptionDetails = getSubscriptionPlanDetails(planName, billingCycle);
    console.log('Subscription plan details:', subscriptionDetails);

    // Calculate next billing date based on subscription interval
    const now = new Date();
    let nextBillingDate;

    if (subscriptionDetails.frequency === 'MONTH') {
      nextBillingDate = new Date(now.getFullYear(), now.getMonth() + subscriptionDetails.frequency_interval, now.getDate());
    } else if (subscriptionDetails.frequency === 'YEAR') {
      nextBillingDate = new Date(now.getFullYear() + subscriptionDetails.frequency_interval, now.getMonth(), now.getDate());
    } else if (subscriptionDetails.frequency === 'DAY') {
      nextBillingDate = new Date(now.getTime() + (subscriptionDetails.frequency_interval * 24 * 60 * 60 * 1000));
    } else if (subscriptionDetails.frequency === 'WEEK') {
      nextBillingDate = new Date(now.getTime() + (subscriptionDetails.frequency_interval * 7 * 24 * 60 * 60 * 1000));
    } else {
      nextBillingDate = new Date(now.getFullYear(), now.getMonth() + 1, now.getDate());
    }

    console.log('Next billing date:', nextBillingDate);

    // Update user's plan
    const updates = {
      plan: planName.toLowerCase(),
      posts_limit: subscriptionDetails.posts_limit,
      posts_count: 0, // Reset post count for new billing cycle
      subscription_status: 'active',
      billing_cycle: billingCycle,
      next_billing_date: nextBillingDate.toISOString(),
      paypal_subscription_id: subscriptionID,
      updated_at: new Date().toISOString()
    };

    console.log('Updating user profile with:', updates);
    const result = await updateUserProfile(userId, updates);

    if (result.error) {
      console.error('Failed to update user profile:', result.error);
      return res.status(500).json({ error: 'Failed to update user plan' });
    }

    // Log the successful subscription
    console.log(`Subscription successful: User ${userId} subscribed to ${planName} plan`);

    res.json({
      success: true,
      message: `Successfully subscribed to ${planName} plan`,
      plan: updates,
      isSubscription: true
    });

  } catch (error) {
    console.error('PayPal subscription activation error:', error);
    res.status(500).json({
      error: 'Subscription activation failed',
      details: error.message
    });
  }
});

// PayPal subscription cancel endpoint
app.post('/api/payments/paypal/subscription-cancel', authenticateToken, async (req, res) => {
  try {
    console.log('Cancel endpoint hit, req.user:', req.user);
    console.log('Request body:', req.body);

    const { subscriptionID } = req.body;
    const userId = req.user?.sub;

    console.log('PayPal subscription cancellation endpoint called');
    console.log('Cancellation details:', {
      userId,
      subscriptionID
    });

    if (!userId) {
      console.error('No userId found in token');
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'No user ID found in token'
      });
    }

    if (!subscriptionID) {
      return res.status(400).json({
        error: 'Subscription ID is required',
        message: 'Please provide a valid subscription ID'
      });
    }

    // Get PayPal access token
    console.log('Getting PayPal access token...');
    const accessToken = await getPayPalAccessToken();
    console.log('Access token obtained successfully');

    // Cancel subscription via PayPal API
    const paypalBaseUrl = process.env.PAYPAL_BASE_URL || 'https://api-m.sandbox.paypal.com';
    console.log('PayPal Base URL:', paypalBaseUrl);
    console.log('Cancelling subscription:', subscriptionID);

    const cancelResponse = await fetch(`${paypalBaseUrl}/v1/billing/subscriptions/${subscriptionID}/cancel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json',
        'PayPal-Request-Id': `CANCEL-${Date.now()}`
      },
      body: JSON.stringify({
        reason: 'Customer requested cancellation'
      })
    });

    if (!cancelResponse.ok) {
      const paypalError = await cancelResponse.json();
      console.error('PayPal cancellation failed:', paypalError);

      // If subscription is already cancelled, still proceed with local update
      if (paypalError.name === 'UNPROCESSABLE_ENTITY' &&
          paypalError.details?.[0]?.issue === 'SUBSCRIPTION_STATUS_INVALID') {
        console.log('Subscription already cancelled at PayPal, updating local status...');
      } else {
        return res.status(400).json({
          error: 'PayPal cancellation failed',
          details: paypalError
        });
      }
    } else {
      console.log('PayPal subscription cancelled successfully');
    }

    // Update user profile - keep current plan but mark as cancelled (non-renewing)
    const updates = {
      subscription_status: 'cancelled', // Mark as cancelled but keep current plan
      paypal_subscription_id: subscriptionID, // Keep the ID for reference
      updated_at: new Date().toISOString()
      // Don't change plan, posts_limit, billing_cycle, or next_billing_date
      // User keeps benefits until next_billing_date
    };

    console.log('Updating user profile with:', updates);

    // Update the profile in the database
    const updateResult = await updateUserProfile(userId, updates);

    if (updateResult.error) {
      console.error('Failed to update user profile after cancellation:', updateResult.error);
      return res.status(500).json({
        error: 'Profile update failed after cancellation',
        details: updateResult.error
      });
    }

    console.log(`Subscription cancelled: User ${userId} subscription marked as cancelled`);

    res.json({
      success: true,
      message: 'Subscription cancelled successfully. You will keep your current plan benefits until your next billing date, then be downgraded to the free plan.',
      plan: updateResult.data || updates
    });

  } catch (error) {
    console.error('PayPal subscription cancellation error:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      error: 'Subscription cancellation failed',
      details: error.message,
      stack: error.stack
    });
  }
});

// Admin Routes
// Check if current user is admin
app.get('/api/admin/status', checkAdminStatus, (req, res) => {
  console.log('Admin status endpoint - req.isAdmin:', req.isAdmin);
  res.json({ isAdmin: req.isAdmin });
});

// Get all users with pagination, search, and filtering
app.get('/api/admin/users', authenticateAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      plan = '',
      status = ''
    } = req.query;

    const offset = (page - 1) * limit;
    const { getAll, getOne } = await import('./sqlite-db.js');

    // Build search and filter conditions
    let whereConditions = [];
    let params = [];

    if (search) {
      whereConditions.push('(p.full_name LIKE ? OR u.email LIKE ?)');
      params.push(`%${search}%`, `%${search}%`);
    }

    if (plan) {
      whereConditions.push('p.plan = ?');
      params.push(plan);
    }

    if (status) {
      whereConditions.push('p.subscription_status = ?');
      params.push(status);
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM users u
      JOIN profiles p ON u.id = p.id
      ${whereClause}
    `;
    const totalResult = getOne(countQuery, params);
    const total = totalResult.total;

    // Get users with pagination
    const usersQuery = `
      SELECT
        u.id, u.email, u.created_at as user_created_at,
        p.full_name, p.plan, p.posts_count, p.posts_limit,
        p.subscription_status, p.billing_cycle, p.next_billing_date,
        p.is_admin, p.created_at, p.updated_at
      FROM users u
      JOIN profiles p ON u.id = p.id
      ${whereClause}
      ORDER BY p.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const users = getAll(usersQuery, [...params, parseInt(limit), offset]);

    // Get post counts for each user
    const usersWithPosts = users.map(user => {
      const postCount = getOne('SELECT COUNT(*) as count FROM posts WHERE user_id = ?', [user.id]);
      return {
        ...user,
        total_posts_created: postCount.count
      };
    });

    res.json({
      users: usersWithPosts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Get specific user details
app.get('/api/admin/users/:userId', authenticateAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { getOne, getAll } = await import('./sqlite-db.js');

    // Get user details
    const user = getOne(`
      SELECT
        u.id, u.email, u.created_at as user_created_at,
        p.full_name, p.plan, p.posts_count, p.posts_limit,
        p.subscription_status, p.billing_cycle, p.next_billing_date,
        p.paypal_subscription_id, p.paypal_order_id,
        p.is_admin, p.created_at, p.updated_at, p.last_post_time
      FROM users u
      JOIN profiles p ON u.id = p.id
      WHERE u.id = ?
    `, [userId]);

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Get user's posts
    const posts = getAll(`
      SELECT id, created_at, updated_at, content, caption, image_url, published
      FROM posts
      WHERE user_id = ?
      ORDER BY created_at DESC
    `, [userId]);

    res.json({
      user,
      posts,
      totalPosts: posts.length
    });
  } catch (error) {
    console.error('Error fetching user details:', error);
    res.status(500).json({ error: 'Failed to fetch user details' });
  }
});

// Update user (admin only)
app.patch('/api/admin/users/:userId', authenticateAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const updates = req.body;

    // Validate that admin is not removing their own admin status
    if (req.user.sub === userId && updates.is_admin === 0) {
      return res.status(400).json({ error: 'Cannot remove your own admin status' });
    }

    const result = await updateUserProfile(userId, {
      ...updates,
      updated_at: new Date().toISOString()
    });

    if (result.error) {
      return res.status(400).json({ error: result.error.message });
    }

    res.json(result.data);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Delete user (admin only)
app.delete('/api/admin/users/:userId', authenticateAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { db } = await import('./sqlite-db.js');

    // Prevent admin from deleting themselves
    if (req.user.sub === userId) {
      return res.status(400).json({ error: 'Cannot delete your own account' });
    }

    // Delete user (cascades to posts and profile due to foreign keys)
    const result = db.prepare('DELETE FROM users WHERE id = ?').run(userId);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({ success: true, message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ error: 'Failed to delete user' });
  }
});

// Add/Remove credits (posts)
app.patch('/api/admin/users/:userId/credits', authenticateAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { credits, action } = req.body; // action: 'add' or 'remove' or 'set'

    const profileResult = await getUserProfile(userId);
    if (profileResult.error) {
      return res.status(404).json({ error: 'User not found' });
    }

    const currentCount = profileResult.data.posts_count;
    let newCount;

    switch (action) {
      case 'add':
        newCount = Math.max(0, currentCount + credits);
        break;
      case 'remove':
        newCount = Math.max(0, currentCount - credits);
        break;
      case 'set':
        newCount = Math.max(0, credits);
        break;
      default:
        return res.status(400).json({ error: 'Invalid action. Use add, remove, or set' });
    }

    const result = await updateUserProfile(userId, {
      posts_count: newCount,
      updated_at: new Date().toISOString()
    });

    if (result.error) {
      return res.status(400).json({ error: result.error.message });
    }

    res.json({
      success: true,
      previousCount: currentCount,
      newCount: newCount,
      user: result.data
    });
  } catch (error) {
    console.error('Error updating credits:', error);
    res.status(500).json({ error: 'Failed to update credits' });
  }
});

// Get user's gallery/posts
app.get('/api/admin/users/:userId/gallery', authenticateAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const { getAll, getOne } = await import('./sqlite-db.js');

    // Get total count
    const totalResult = getOne('SELECT COUNT(*) as total FROM posts WHERE user_id = ?', [userId]);
    const total = totalResult.total;

    // Get posts with pagination
    const posts = getAll(`
      SELECT id, created_at, updated_at, content, caption, image_url, published
      FROM posts
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `, [userId, parseInt(limit), offset]);

    res.json({
      posts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching user gallery:', error);
    res.status(500).json({ error: 'Failed to fetch user gallery' });
  }
});

// Change user plan
app.patch('/api/admin/users/:userId/plan', authenticateAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const { plan, billingCycle } = req.body;

    const validPlans = ['free', 'basic', 'pro', 'ultra'];
    const validCycles = ['monthly', 'yearly'];

    if (!validPlans.includes(plan)) {
      return res.status(400).json({ error: 'Invalid plan' });
    }

    if (billingCycle && !validCycles.includes(billingCycle)) {
      return res.status(400).json({ error: 'Invalid billing cycle' });
    }

    // Get plan limits
    const planLimits = {
      free: 3,
      basic: 50,
      pro: 200,
      ultra: 1000
    };

    const updates = {
      plan,
      posts_limit: planLimits[plan],
      posts_count: 0, // Reset count when changing plan
      subscription_status: plan === 'free' ? 'active' : 'active',
      updated_at: new Date().toISOString()
    };

    if (billingCycle) {
      updates.billing_cycle = billingCycle;
      // Set next billing date to 1 month/year from now
      const nextBilling = new Date();
      if (billingCycle === 'monthly') {
        nextBilling.setMonth(nextBilling.getMonth() + 1);
      } else {
        nextBilling.setFullYear(nextBilling.getFullYear() + 1);
      }
      updates.next_billing_date = nextBilling.toISOString();
    } else if (plan === 'free') {
      updates.billing_cycle = null;
      updates.next_billing_date = null;
    }

    const result = await updateUserProfile(userId, updates);

    if (result.error) {
      return res.status(400).json({ error: result.error.message });
    }

    res.json({
      success: true,
      message: `User plan changed to ${plan}`,
      user: result.data
    });
  } catch (error) {
    console.error('Error changing user plan:', error);
    res.status(500).json({ error: 'Failed to change user plan' });
  }
});

// Analytics endpoints for admin panel

// Get overall platform statistics
app.get('/api/admin/stats/overview', authenticateAdmin, async (req, res) => {
  try {
    // Get total users
    const totalUsers = getOne('SELECT COUNT(*) as count FROM users') || { count: 0 };

    // Get total posts
    const totalPosts = getOne('SELECT COUNT(*) as count FROM posts') || { count: 0 };

    // Get users by plan
    const planDistribution = getAll(`
      SELECT plan, COUNT(*) as count
      FROM profiles
      GROUP BY plan
    `) || [];

    // Get active subscriptions
    const activeSubscriptions = getOne(`
      SELECT COUNT(*) as count
      FROM profiles
      WHERE subscription_status = 'active' AND plan != 'free'
    `) || { count: 0 };

    // Get posts created today
    const today = new Date().toISOString().split('T')[0];
    const postsToday = getOne(`
      SELECT COUNT(*) as count
      FROM posts
      WHERE DATE(created_at) = ?
    `, [today]) || { count: 0 };

    // Get new users today
    const newUsersToday = getOne(`
      SELECT COUNT(*) as count
      FROM users
      WHERE DATE(created_at) = ?
    `, [today]) || { count: 0 };

    // Get posts created in the last minute (using same approach as individual user rate limiting)
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000).toISOString();
    const postsInLastMinute = getOne(`
      SELECT COUNT(*) as count
      FROM posts
      WHERE created_at >= ?
    `, [oneMinuteAgo]) || { count: 0 };

    // Define global rate limits for different plans
    const globalRateLimits = {
      free: 30,  // Global limit for free users (posts per minute across all users)
      basic: 65  // Global limit for basic users (posts per minute across all users)
    };

    res.json({
      totalUsers: totalUsers.count || 0,
      totalPosts: totalPosts.count || 0,
      planDistribution: planDistribution || [],
      activeSubscriptions: activeSubscriptions.count || 0,
      postsToday: postsToday.count || 0,
      newUsersToday: newUsersToday.count || 0,
      postsInLastMinute: postsInLastMinute.count || 0,
      globalRateLimits: globalRateLimits
    });
  } catch (error) {
    console.error('Error fetching overview stats:', error);
    res.status(500).json({ error: 'Failed to fetch overview statistics' });
  }
});

// Get currently online users count
app.get('/api/admin/stats/users-online', authenticateAdmin, async (req, res) => {
  try {
    const result = getOnlineUsersCount();
    res.json({ count: result.count });
  } catch (error) {
    console.error('Error fetching online users count:', error);
    res.status(500).json({ error: 'Failed to fetch online users count' });
  }
});

// Get user growth analytics
app.get('/api/admin/stats/user-growth', authenticateAdmin, async (req, res) => {
  try {
    const { period = '30', groupBy = 'day' } = req.query;

    let dateFormat, dateGroup;
    switch (groupBy) {
      case 'hour':
        dateFormat = '%Y-%m-%d %H:00:00';
        dateGroup = 'hour';
        break;
      case 'day':
        dateFormat = '%Y-%m-%d';
        dateGroup = 'day';
        break;
      case 'week':
        dateFormat = '%Y-W%W';
        dateGroup = 'week';
        break;
      case 'month':
        dateFormat = '%Y-%m';
        dateGroup = 'month';
        break;
      default:
        dateFormat = '%Y-%m-%d';
        dateGroup = 'day';
    }

    const userGrowth = getAll(`
      SELECT
        strftime('${dateFormat}', created_at) as period,
        COUNT(*) as new_users
      FROM users
      WHERE created_at >= datetime('now', '-${parseInt(period)} days')
      GROUP BY strftime('${dateFormat}', created_at)
      ORDER BY period ASC
    `);

    res.json({ userGrowth, period, groupBy });
  } catch (error) {
    console.error('Error fetching user growth stats:', error);
    res.status(500).json({ error: 'Failed to fetch user growth statistics' });
  }
});

// Get post activity analytics
app.get('/api/admin/stats/post-activity', authenticateAdmin, async (req, res) => {
  try {
    const { period = '30', groupBy = 'day' } = req.query;

    let dateFormat;
    switch (groupBy) {
      case 'hour':
        dateFormat = '%Y-%m-%d %H:00:00';
        break;
      case 'day':
        dateFormat = '%Y-%m-%d';
        break;
      case 'week':
        dateFormat = '%Y-W%W';
        break;
      case 'month':
        dateFormat = '%Y-%m';
        break;
      default:
        dateFormat = '%Y-%m-%d';
    }

    const postActivity = getAll(`
      SELECT
        strftime('${dateFormat}', created_at) as period,
        COUNT(*) as posts_created
      FROM posts
      WHERE created_at >= datetime('now', '-${parseInt(period)} days')
      GROUP BY strftime('${dateFormat}', created_at)
      ORDER BY period ASC
    `);

    // Get posts by plan
    const postsByPlan = getAll(`
      SELECT
        p.plan,
        COUNT(posts.id) as post_count
      FROM posts
      JOIN profiles p ON posts.user_id = p.id
      WHERE posts.created_at >= datetime('now', '-${parseInt(period)} days')
      GROUP BY p.plan
    `);

    res.json({ postActivity, postsByPlan, period, groupBy });
  } catch (error) {
    console.error('Error fetching post activity stats:', error);
    res.status(500).json({ error: 'Failed to fetch post activity statistics' });
  }
});

// Get revenue analytics
app.get('/api/admin/stats/revenue', authenticateAdmin, async (req, res) => {
  try {
    const { period = '30', groupBy = 'day' } = req.query;

    // Plan pricing (in USD)
    const planPricing = {
      basic: { monthly: 9.99, yearly: 99.99 },
      pro: { monthly: 19.99, yearly: 199.99 },
      ultra: { monthly: 39.99, yearly: 399.99 }
    };

    let dateFormat;
    switch (groupBy) {
      case 'day':
        dateFormat = '%Y-%m-%d';
        break;
      case 'week':
        dateFormat = '%Y-W%W';
        break;
      case 'month':
        dateFormat = '%Y-%m';
        break;
      default:
        dateFormat = '%Y-%m-%d';
    }

    // Get subscription data
    const subscriptions = getAll(`
      SELECT
        plan,
        billing_cycle,
        strftime('${dateFormat}', created_at) as period,
        COUNT(*) as subscription_count
      FROM profiles
      WHERE plan != 'free'
        AND subscription_status = 'active'
        AND created_at >= datetime('now', '-${parseInt(period)} days')
      GROUP BY plan, billing_cycle, strftime('${dateFormat}', created_at)
      ORDER BY period ASC
    `);

    // Calculate estimated revenue
    const revenueData = subscriptions.map(sub => {
      const pricing = planPricing[sub.plan];
      const revenue = pricing ? pricing[sub.billing_cycle] * sub.subscription_count : 0;
      return {
        ...sub,
        estimated_revenue: revenue
      };
    });

    // Get total revenue by plan
    const totalRevenueByPlan = getAll(`
      SELECT
        plan,
        billing_cycle,
        COUNT(*) as active_subscriptions
      FROM profiles
      WHERE plan != 'free' AND subscription_status = 'active'
      GROUP BY plan, billing_cycle
    `).map(sub => {
      const pricing = planPricing[sub.plan];
      const revenue = pricing ? pricing[sub.billing_cycle] * sub.active_subscriptions : 0;
      return {
        ...sub,
        estimated_monthly_revenue: revenue
      };
    });

    res.json({
      revenueData,
      totalRevenueByPlan,
      period,
      groupBy,
      planPricing
    });
  } catch (error) {
    console.error('Error fetching revenue stats:', error);
    res.status(500).json({ error: 'Failed to fetch revenue statistics' });
  }
});

// Helper function to get rate limit based on plan
const getRateLimit = (plan) => {
  switch (plan) {
    case 'free':
    case 'basic':
      return 1; // 1 post per minute
    case 'pro':
      return 3; // 3 posts per minute
    case 'ultra':
      return 5; // 5 posts per minute
    default:
      return 1;
  }
};

// Helper function to check if a user has unlimited posts
const hasUnlimitedPosts = async () => {
  // Always return false since we're removing the unlimited posts feature
  return false;
};

// Helper function to save a post or update an existing one
// Function to save a post or update an existing one
const savePost = async (userId, content, caption, imagePath, existingPostId = null) => {
  console.log(`savePost called with existingPostId: ${existingPostId || 'null'}`);
  try {
    // Check if user has reached their post limit
    const profileResult = await getUserProfile(userId);

    if (profileResult.error) {
      return { error: 'User profile not found' };
    }

    const profile = profileResult.data;

    // Check if the user has unlimited posts
    const unlimited = await hasUnlimitedPosts(userId);

    // Only check post limit if this is a new post and the user doesn't have unlimited posts
    if (!existingPostId && !unlimited && profile.posts_count >= profile.posts_limit) {
      return { error: 'Post limit reached' };
    }

    // Check rate limiting
    const now = new Date();
    const rateLimit = getRateLimit(profile.plan);

    if (profile.last_post_time) {
      const lastPostTime = new Date(profile.last_post_time);
      const timeDiffSeconds = (now - lastPostTime) / 1000;
      const requiredWaitTimeSeconds = 60 / rateLimit; // Convert posts per minute to seconds between posts

      if (timeDiffSeconds < requiredWaitTimeSeconds) {
        const waitTimeLeft = Math.ceil(requiredWaitTimeSeconds - timeDiffSeconds);
        return {
          error: `Rate limit exceeded. Please wait ${waitTimeLeft} seconds before generating another post.`,
          waitTimeLeft
        };
      }
    }

    // Ensure caption is not empty or undefined
    const finalCaption = caption || '';
    const { db } = await import('./sqlite-db.js');
    let postId;

    // Parse the content to extract caption and hashtags parts
    let parsedContent;
    let captionPart = '';
    let hashtagsPart = '';

    try {
      if (typeof content === 'string') {
        parsedContent = JSON.parse(content);

        // Extract caption and hashtag parts if they exist in the content
        if (parsedContent.captionPart) captionPart = parsedContent.captionPart;
        if (parsedContent.hashtagsPart) hashtagsPart = parsedContent.hashtagsPart;
      } else {
        parsedContent = content;
        if (content.captionPart) captionPart = content.captionPart;
        if (content.hashtagsPart) hashtagsPart = content.hashtagsPart;
      }
    } catch (err) {
      console.error('Error parsing content:', err);
      parsedContent = { error: 'Invalid content format' };
    }

    // If we don't have caption parts in the content but have a full caption,
    // try to split it into parts
    if ((!captionPart || !hashtagsPart) && finalCaption) {
      const parts = finalCaption.split('\n\n');
      if (parts.length >= 2) {
        captionPart = parts[0];
        hashtagsPart = parts[1];
      } else {
        captionPart = finalCaption;
        hashtagsPart = '';
      }
    }

    // Create user's posts directory if it doesn't exist
    const postsDir = path.join(__dirname, '..', 'data', 'posts');
    const userPostsDir = path.join(postsDir, 'db', userId);

    if (!fs.existsSync(postsDir)) {
      fs.mkdirSync(postsDir, { recursive: true });
    }

    if (!fs.existsSync(userPostsDir)) {
      fs.mkdirSync(userPostsDir, { recursive: true });
    }

    if (existingPostId) {
      // Update an existing post
      postId = existingPostId;
      console.log(`Updating existing post with ID: ${postId} for user ${userId}`);

      // First check if the post exists and belongs to this user
      const existingPost = db.prepare('SELECT * FROM posts WHERE id = ? AND user_id = ?').get(postId, userId);

      // Log whether we found the post
      if (existingPost) {
        console.log(`Found existing post ${postId}, owned by ${existingPost.user_id}`);
      } else {
        console.log(`Post ${postId} not found or not owned by user ${userId}`);
      }

      if (!existingPost) {
        return { error: 'Post not found or unauthorized' };
      }

      // Update the post with the new image (but keep existing caption if new one is empty)
      const updateCaption = finalCaption ? finalCaption : existingPost.caption;

      // Update the database record
      db.prepare(`
        UPDATE posts
        SET image_url = ?, updated_at = CURRENT_TIMESTAMP, caption = ?
        WHERE id = ? AND user_id = ?
      `).run(imagePath || null, updateCaption, postId, userId);

      console.log(`Post ${postId} updated with new image`);

      // Update the post JSON file
      const postFilePath = path.join(userPostsDir, `${postId}.json`);

      // If the file exists, read it first to preserve any existing data
      let postData = {};
      if (fs.existsSync(postFilePath)) {
        try {
          const fileContent = fs.readFileSync(postFilePath, 'utf8');
          postData = JSON.parse(fileContent);
        } catch (err) {
          console.error(`Error reading post file ${postFilePath}:`, err);
        }
      }

      // Update the post data
      postData = {
        ...postData,
        id: postId,
        userId: userId,
        content: parsedContent,
        caption: updateCaption,
        captionPart: captionPart,
        hashtagsPart: hashtagsPart,
        imageUrl: imagePath,
        updatedAt: now.toISOString()
      };

      // Write the updated post data to the file
      fs.writeFileSync(postFilePath, JSON.stringify(postData, null, 2));
      console.log(`Post data saved to file: ${postFilePath}`);

    } else {
      // Create a new post
      postId = uuidv4();

      // Insert the new post into the database with explicit timestamp
      db.prepare(`
        INSERT INTO posts (id, user_id, content, caption, image_url, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `).run(postId, userId, content, finalCaption, imagePath || null, now.toISOString());

      // Update the posts count and last post time
      await updateUserProfile(userId, {
        posts_count: profile.posts_count + 1,
        last_post_time: now.toISOString()
      });

      console.log(`New post ${postId} created for user ${userId}`);

      // Create a JSON file for the post
      const postFilePath = path.join(userPostsDir, `${postId}.json`);

      // Prepare the post data
      const postData = {
        id: postId,
        userId: userId,
        content: parsedContent,
        caption: finalCaption,
        captionPart: captionPart,
        hashtagsPart: hashtagsPart,
        imageUrl: imagePath,
        createdAt: now.toISOString(),
        updatedAt: now.toISOString()
      };

      // Write the post data to the file
      fs.writeFileSync(postFilePath, JSON.stringify(postData, null, 2));
      console.log(`Post data saved to file: ${postFilePath}`);
    }

    // Verify the post was saved/updated correctly
    const savedPost = db.prepare('SELECT * FROM posts WHERE id = ?').get(postId);
    console.log('Post content saved:', savedPost.caption ? savedPost.caption.substring(0, 100) + '...' : 'null');

    return { success: true, postId };
  } catch (error) {
    console.error('Error saving post:', error);
    return { error: 'Failed to save post' };
  }
};

// Posts routes
app.post('/api/posts', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;
    const { content, caption, imageUrl, postId } = req.body;

    if (!content) {
      return res.status(400).json({ error: 'Content is required' });
    }

    // If postId is provided, update the existing post
    const result = await savePost(userId, content, caption, imageUrl, postId);

    if (result.error) {
      return res.status(400).json({ error: result.error });
    }

    res.status(201).json(result);
  } catch (error) {
    console.error('Create post error:', error);
    res.status(500).json({ error: 'Failed to create post' });
  }
});

// Check rate limit for current user before generating content
app.get('/api/check-rate-limit', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;

    // Get user profile to check plan
    const profileResult = await getUserProfile(userId);
    if (profileResult.error) {
      return res.status(404).json({ error: 'User profile not found' });
    }

    const profile = profileResult.data;

    // Get rate limit for user's plan
    const rateLimit = getRateLimit(profile.plan);

    // Query posts created in the last minute for this user
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000).toISOString();
    const recentPosts = getAll(`
      SELECT created_at
      FROM posts
      WHERE user_id = ?
      AND created_at >= ?
      ORDER BY created_at ASC
    `, [userId, oneMinuteAgo]);

    const postsInLastMinute = recentPosts.length;

    // Check if user has exceeded rate limit
    if (postsInLastMinute >= rateLimit) {
      // Find the oldest post that needs to expire for user to make another post
      // We need to wait until the oldest post is more than 60 seconds old
      const oldestPostTime = new Date(recentPosts[0].created_at); // First post since we ordered ASC
      const now = new Date();
      const ageOfOldestPost = (now - oldestPostTime) / 1000; // seconds

      // Calculate how much longer we need to wait for the oldest post to be 60+ seconds old
      const waitTimeSeconds = Math.ceil(60 - ageOfOldestPost);

      // If wait time is negative or zero, something is wrong with our calculation, default to 1
      const actualWaitTime = waitTimeSeconds > 0 ? waitTimeSeconds : 1;

      return res.status(429).json({
        rateLimitExceeded: true,
        error: `Rate limit exceeded. ${profile.plan.toUpperCase()} plan allows ${rateLimit} post${rateLimit > 1 ? 's' : ''} per minute. Please wait ${actualWaitTime} seconds.`,
        postsInLastMinute,
        rateLimit,
        waitTimeSeconds: actualWaitTime,
        plan: profile.plan,
        debug: {
          oldestPostTime: oldestPostTime.toISOString(),
          now: now.toISOString(),
          ageOfOldestPost: Math.round(ageOfOldestPost),
          calculatedWait: waitTimeSeconds
        }
      });
    }

    res.json({
      rateLimitExceeded: false,
      postsInLastMinute,
      rateLimit,
      plan: profile.plan,
      remainingPosts: rateLimit - postsInLastMinute
    });

  } catch (error) {
    console.error('Error checking rate limit:', error);
    res.status(500).json({ error: 'Failed to check rate limit' });
  }
});

// Get recent posts count for current user (must be before the :postId route)
app.get('/api/posts/recent-count', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;
    const { minutes = 1 } = req.query;

    // Query posts created in the last N minutes for this user (using same approach as rate limiting)
    const minutesAgo = new Date(Date.now() - parseInt(minutes) * 60 * 1000).toISOString();
    const recentPosts = getAll(`
      SELECT COUNT(*) as count
      FROM posts
      WHERE user_id = ?
      AND created_at >= ?
    `, [userId, minutesAgo]);

    const count = recentPosts[0]?.count || 0;

    res.json({
      count,
      timeframe: `${minutes} minute(s)`,
      userId
    });
  } catch (error) {
    console.error('Error fetching recent posts count:', error);
    res.status(500).json({ error: 'Failed to fetch recent posts count' });
  }
});

// Get all posts for the authenticated user with pagination
app.get('/api/posts', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;
    const { page = 1, limit = 6 } = req.query; // Default to 6 posts per page
    const offset = (page - 1) * limit;

    // Get total count of posts for pagination info
    const { db, getOne } = await import('./sqlite-db.js');
    const totalResult = getOne('SELECT COUNT(*) as total FROM posts WHERE user_id = ?', [userId]);
    const total = totalResult.total;

    // Get posts for the current page from the database
    const posts = db.prepare('SELECT * FROM posts WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?').all(userId, parseInt(limit), offset);

    // Check if we have a posts directory for this user
    const postsDir = path.join(__dirname, '..', 'data', 'posts');
    const userPostsDir = path.join(postsDir, 'db', userId);

    // Create the directory if it doesn't exist
    if (!fs.existsSync(userPostsDir)) {
      fs.mkdirSync(userPostsDir, { recursive: true });
      console.log(`Created user posts directory: ${userPostsDir}`);
    }

    // Get all JSON files for this user
    let postFiles = [];
    try {
      if (fs.existsSync(userPostsDir)) {
        postFiles = fs.readdirSync(userPostsDir)
          .filter(file => file.endsWith('.json'))
          .map(file => {
            try {
              const filePath = path.join(userPostsDir, file);
              const fileContent = fs.readFileSync(filePath, 'utf8');
              return JSON.parse(fileContent);
            } catch (err) {
              console.error(`Error reading post file ${file}:`, err);
              return null;
            }
          })
          .filter(data => data !== null);
      }
    } catch (err) {
      console.error(`Error reading user posts directory ${userPostsDir}:`, err);
    }

    // Create a map of post IDs to file data for quick lookup
    const postFileMap = new Map();
    postFiles.forEach(fileData => {
      if (fileData && fileData.id) {
        postFileMap.set(fileData.id, fileData);
      }
    });

    // Format the response
    const formattedPosts = await Promise.all(posts.map(async post => {
      // Parse the content JSON
      let parsedContent = {};
      try {
        parsedContent = JSON.parse(post.content);

        // Check if we have file data for this post
        const fileData = postFileMap.get(post.id);
        if (fileData) {
          // Merge the content from the file with the database content
          if (fileData.content) {
            parsedContent = {
              ...parsedContent,
              ...fileData.content
            };
          }

          // Use caption parts from the file if available
          if (fileData.captionPart) {
            parsedContent.captionPart = fileData.captionPart;
          }

          if (fileData.hashtagsPart) {
            parsedContent.hashtagsPart = fileData.hashtagsPart;
          }
        }

        // Add caption and hashtag parts if they don't exist
        if (post.caption && (!parsedContent.captionPart || !parsedContent.hashtagsPart)) {
          const parts = post.caption.split('\n\n');
          if (parts.length >= 2) {
            parsedContent.captionPart = parts[0];
            parsedContent.hashtagsPart = parts[1];
          } else {
            parsedContent.captionPart = post.caption;
            parsedContent.hashtagsPart = '';
          }

          // If we have a file for this post but no caption parts, update the file
          if (postFileMap.has(post.id) && (!postFileMap.get(post.id).captionPart || !postFileMap.get(post.id).hashtagsPart)) {
            try {
              const fileData = postFileMap.get(post.id);
              fileData.captionPart = parsedContent.captionPart;
              fileData.hashtagsPart = parsedContent.hashtagsPart;

              const filePath = path.join(userPostsDir, `${post.id}.json`);
              fs.writeFileSync(filePath, JSON.stringify(fileData, null, 2));
              console.log(`Updated caption parts in file for post ${post.id}`);
            } catch (updateErr) {
              console.error(`Error updating file for post ${post.id}:`, updateErr);
            }
          }
        }
      } catch (err) {
        console.error('Error parsing post content:', err);
      }

      // Get the image data if available
      let imageData = null;

      // First check if there's a post-specific image file
      const postImagePath = path.join(imagesDir, `post_${post.id}.jpeg`);
      const postImagePathPng = path.join(imagesDir, `post_${post.id}.png`);

      if (fs.existsSync(postImagePath)) {
        try {
          const fileData = fs.readFileSync(postImagePath);
          imageData = `data:image/jpeg;base64,${fileData.toString('base64')}`;
        } catch (err) {
          console.error(`Error reading post-specific image file ${postImagePath}:`, err);
        }
      } else if (fs.existsSync(postImagePathPng)) {
        try {
          const fileData = fs.readFileSync(postImagePathPng);
          imageData = `data:image/png;base64,${fileData.toString('base64')}`;
        } catch (err) {
          console.error(`Error reading post-specific PNG image file ${postImagePathPng}:`, err);
        }
      } else if (post.image_url) {
        // Fall back to the original image URL from the database
        const imageId = post.image_url.split('/').pop();

        try {
          // Find the image file
          const files = fs.readdirSync(imagesDir);
          const imageFile = files.find(file => file.startsWith(imageId));

          if (imageFile) {
            const filePath = path.join(imagesDir, imageFile);
            try {
              const fileData = fs.readFileSync(filePath);
              const ext = path.extname(imageFile).toLowerCase();
              let mimeType = 'image/jpeg'; // Default

              if (ext === '.png') mimeType = 'image/png';
              else if (ext === '.gif') mimeType = 'image/gif';
              else if (ext === '.webp') mimeType = 'image/webp';

              imageData = `data:${mimeType};base64,${fileData.toString('base64')}`;

              // Create a post-specific copy of the image if it doesn't exist
              try {
                const newFileName = `post_${post.id}${ext}`;
                const newFilePath = path.join(imagesDir, newFileName);

                if (!fs.existsSync(newFilePath)) {
                  fs.copyFileSync(filePath, newFilePath);
                  console.log(`Created post-specific copy of image for post ${post.id}`);
                }
              } catch (copyErr) {
                console.error(`Error creating post-specific copy of image for post ${post.id}:`, copyErr);
              }
            } catch (err) {
              console.error(`Error reading image file ${filePath}:`, err);
            }
          }
        } catch (fsError) {
          console.error(`Error accessing image directory for post ${post.id}:`, fsError);
        }
      }

      // If we don't have a JSON file for this post, create one
      if (!postFileMap.has(post.id)) {
        try {
          const postData = {
            id: post.id,
            userId: userId,
            content: parsedContent,
            caption: post.caption,
            captionPart: parsedContent.captionPart || '',
            hashtagsPart: parsedContent.hashtagsPart || '',
            imageUrl: post.image_url,
            createdAt: post.created_at,
            updatedAt: new Date().toISOString()
          };

          const filePath = path.join(userPostsDir, `${post.id}.json`);
          fs.writeFileSync(filePath, JSON.stringify(postData, null, 2));
          console.log(`Created JSON file for post ${post.id}`);
        } catch (fileErr) {
          console.error(`Error creating JSON file for post ${post.id}:`, fileErr);
        }
      }

      return {
        id: post.id,
        content: parsedContent,
        caption: post.caption,
        imageUrl: post.image_url,
        imageData: imageData,
        createdAt: post.created_at
      };
    }));

    res.json({
      posts: formattedPosts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get posts error:', error);
    res.status(500).json({ error: 'Failed to get posts' });
  }
});

// Get post by ID
app.get('/api/posts/:postId', async (req, res) => {
  try {
    const { postId } = req.params;
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!postId) {
      return res.status(400).json({ error: 'Post ID is required' });
    }

    console.log(`Fetching post with ID: ${postId}, auth token present: ${!!token}`);

    // Get the post from the database
    const { db } = await import('./sqlite-db.js');
    const post = db.prepare('SELECT * FROM posts WHERE id = ?').get(postId);

    if (!post) {
      console.log(`Post not found in database: ${postId}`);
      return res.status(404).json({ error: 'Post not found' });
    }

    console.log(`Post found in database: ${postId}, user_id: ${post.user_id}`);

    // If user is authenticated, verify they own the post
    let isOwner = false;
    let userId = null;
    if (token) {
      try {
        const user = verifyToken(token);
        if (user) {
          userId = user.sub;
          console.log(`User authenticated: ${userId}, post owner: ${post.user_id}`);
          isOwner = userId === post.user_id;

          // If not the owner, return 403
          if (!isOwner) {
            console.log(`Access denied: User ${userId} attempted to access post ${postId} owned by ${post.user_id}`);
            return res.status(403).json({ error: 'You do not have permission to access this post' });
          }
        } else {
          console.log(`Invalid token provided for post ${postId}`);
          return res.status(401).json({ error: 'Invalid authentication token' });
        }
      } catch (tokenError) {
        console.error(`Error verifying token for post ${postId}:`, tokenError);
        return res.status(401).json({ error: 'Invalid authentication token' });
      }
    } else {
      console.log(`No authentication token provided for post ${postId}`);
      // We'll still return the post data but with a warning
      console.log(`Warning: Unauthenticated access to post ${postId}`);
    }

    // Check if we have a JSON file for this post
    let postFileData = null;
    const postsDir = path.join(__dirname, '..', 'data', 'posts');
    const userPostsDir = path.join(postsDir, 'db', post.user_id);
    const postFilePath = path.join(userPostsDir, `${postId}.json`);

    if (fs.existsSync(postFilePath)) {
      try {
        console.log(`Found post JSON file: ${postFilePath}`);
        const fileContent = fs.readFileSync(postFilePath, 'utf8');
        postFileData = JSON.parse(fileContent);
        console.log('Successfully loaded post data from JSON file');
      } catch (err) {
        console.error(`Error reading post file ${postFilePath}:`, err);
      }
    } else {
      console.log(`No JSON file found for post ${postId}, will use database data only`);

      // Create the directory if it doesn't exist
      if (!fs.existsSync(userPostsDir)) {
        fs.mkdirSync(userPostsDir, { recursive: true });
        console.log(`Created user posts directory: ${userPostsDir}`);
      }
    }

    // Get the image data if available
    let imageData = null;

    // First check if there's a post-specific image file
    const postImagePath = path.join(imagesDir, `post_${postId}.jpeg`);
    const postImagePathPng = path.join(imagesDir, `post_${postId}.png`);

    if (fs.existsSync(postImagePath)) {
      console.log(`Found post-specific image file: ${postImagePath}`);
      try {
        const fileData = fs.readFileSync(postImagePath);
        imageData = `data:image/jpeg;base64,${fileData.toString('base64')}`;
        console.log(`Image data loaded successfully from post-specific file, data length: ${imageData.length}`);
      } catch (err) {
        console.error(`Error reading post-specific image file ${postImagePath}:`, err);
      }
    } else if (fs.existsSync(postImagePathPng)) {
      console.log(`Found post-specific PNG image file: ${postImagePathPng}`);
      try {
        const fileData = fs.readFileSync(postImagePathPng);
        imageData = `data:image/png;base64,${fileData.toString('base64')}`;
        console.log(`Image data loaded successfully from post-specific PNG file, data length: ${imageData.length}`);
      } catch (err) {
        console.error(`Error reading post-specific PNG image file ${postImagePathPng}:`, err);
      }
    } else if (post.image_url) {
      // Fall back to the original image URL from the database
      const imageId = post.image_url.split('/').pop();
      console.log(`Fetching image data for ID: ${imageId}`);

      try {
        // Find the image file
        const files = fs.readdirSync(imagesDir);
        const imageFile = files.find(file => file.startsWith(imageId));

        if (imageFile) {
          console.log(`Image file found: ${imageFile}`);
          const filePath = path.join(imagesDir, imageFile);
          try {
            const fileData = fs.readFileSync(filePath);
            const ext = path.extname(imageFile).toLowerCase();
            let mimeType = 'image/jpeg'; // Default

            if (ext === '.png') mimeType = 'image/png';
            else if (ext === '.gif') mimeType = 'image/gif';
            else if (ext === '.webp') mimeType = 'image/webp';

            imageData = `data:${mimeType};base64,${fileData.toString('base64')}`;
            console.log(`Image data loaded successfully for ${imageId}, data length: ${imageData.length}`);

            // If we found the image but don't have a post-specific copy, create one
            if (isOwner && userId) {
              try {
                const newFileName = `post_${postId}${ext}`;
                const newFilePath = path.join(imagesDir, newFileName);

                // Copy the file if it doesn't already exist
                if (!fs.existsSync(newFilePath)) {
                  fs.copyFileSync(filePath, newFilePath);
                  console.log(`Created post-specific copy of image: ${newFilePath}`);
                }
              } catch (copyErr) {
                console.error('Error creating post-specific copy of image:', copyErr);
              }
            }
          } catch (err) {
            console.error(`Error reading image file ${filePath}:`, err);
          }
        } else {
          console.log(`Image file not found for ID: ${imageId}`);

          // Check if the image_url is a full data URL
          if (post.image_url.startsWith('data:')) {
            console.log('Image URL is a data URL, using directly');
            imageData = post.image_url;
          }
        }
      } catch (fsError) {
        console.error(`Error accessing image directory for post ${postId}:`, fsError);
      }
    } else {
      console.log(`No image URL found for post ${postId}`);
    }

    // Parse the content JSON from the database
    let parsedContent = {};
    try {
      // Only parse if it's a string
      if (typeof post.content === 'string') {
        parsedContent = JSON.parse(post.content);
        console.log('Successfully parsed post content JSON from database');
      } else {
        console.log('Post content is not a string, using as is');
        parsedContent = post.content;
      }

      // If we have post file data, merge it with the database content
      if (postFileData && postFileData.content) {
        console.log('Merging content from JSON file with database content');
        parsedContent = {
          ...parsedContent,
          ...postFileData.content
        };
      }

      // Split the caption into parts if they're not already in the content
      if (post.caption && (!parsedContent.captionPart || !parsedContent.hashtagsPart)) {
        console.log('Adding captionPart and hashtagsPart to content object');

        // First check if we have them in the JSON file
        if (postFileData && postFileData.captionPart) {
          parsedContent.captionPart = postFileData.captionPart;
          console.log('Using captionPart from JSON file');
        }

        if (postFileData && postFileData.hashtagsPart) {
          parsedContent.hashtagsPart = postFileData.hashtagsPart;
          console.log('Using hashtagsPart from JSON file');
        }

        // If we still don't have them, split the caption
        if (!parsedContent.captionPart || !parsedContent.hashtagsPart) {
          // Split by double newline if present
          const parts = post.caption.split('\n\n');
          if (parts.length >= 2) {
            parsedContent.captionPart = parts[0];
            parsedContent.hashtagsPart = parts[1];
          } else {
            // If no clear separation, assume it's all caption
            parsedContent.captionPart = post.caption;
            parsedContent.hashtagsPart = '';
          }

          // Prevent string substring errors with proper length checking
          console.log('Split caption into parts:', {
            captionPart: parsedContent.captionPart ?
              (parsedContent.captionPart.substring(0, Math.min(50, parsedContent.captionPart.length)) +
               (parsedContent.captionPart.length > 50 ? '...' : '')) : '',
            hashtagsPart: parsedContent.hashtagsPart ?
              (parsedContent.hashtagsPart.substring(0, Math.min(50, parsedContent.hashtagsPart.length)) +
               (parsedContent.hashtagsPart.length > 50 ? '...' : '')) : ''
          });
        }
      }
    } catch (err) {
      console.error('Error parsing post content:', err);
      // If parsing fails, return the raw content
      parsedContent = post.content;
    }

    // If we have post file data but no JSON file, create one
    if (isOwner && userId && !postFileData) {
      try {
        console.log(`Creating JSON file for post ${postId}`);

        const postData = {
          id: postId,
          userId: post.user_id,
          content: parsedContent,
          caption: post.caption,
          captionPart: parsedContent.captionPart || '',
          hashtagsPart: parsedContent.hashtagsPart || '',
          imageUrl: post.image_url,
          createdAt: post.created_at,
          updatedAt: new Date().toISOString()
        };

        fs.writeFileSync(postFilePath, JSON.stringify(postData, null, 2));
        console.log(`Created JSON file for post ${postId}: ${postFilePath}`);
      } catch (fileErr) {
        console.error(`Error creating JSON file for post ${postId}:`, fileErr);
      }
    }

    // Log the raw post data for debugging
    console.log('Raw post data from database:', {
      id: post.id,
      user_id: post.user_id,
      content: typeof post.content === 'string' ? post.content.substring(0, 100) + '...' : 'not a string',
      caption: post.caption ? post.caption.substring(0, 100) + '...' : 'null',
      image_url: post.image_url
    });

    // Format the response
    const response = {
      id: post.id,
      userId: post.user_id,
      content: parsedContent,
      caption: post.caption,
      imageUrl: post.image_url,
      imageData: imageData,
      createdAt: post.created_at,
      isOwner: isOwner,
      // Add a flag to indicate if image loading was attempted but failed
      imageLoadAttempted: !!post.image_url
    };

    console.log(`Successfully returning post data for ${postId}, image data present: ${!!imageData}, content type: ${typeof parsedContent}, caption present: ${!!post.caption}`);
    res.json(response);
  } catch (error) {
    console.error('Get post error:', error);
    res.status(500).json({ error: 'Failed to get post', details: error.message });
  }
});

// Delete post by ID
app.delete('/api/posts/:postId', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.sub;
    const { postId } = req.params;

    if (!postId) {
      return res.status(400).json({ error: 'Post ID is required' });
    }

    // Get the post from the database to verify ownership
    const { db } = await import('./sqlite-db.js');
    const post = db.prepare('SELECT * FROM posts WHERE id = ?').get(postId);

    if (!post) {
      return res.status(404).json({ error: 'Post not found' });
    }

    // Verify the user owns the post
    if (post.user_id !== userId) {
      return res.status(403).json({ error: 'You do not have permission to delete this post' });
    }

    // Delete the post from the database
    db.prepare('DELETE FROM posts WHERE id = ?').run(postId);

    // Delete the post JSON file if it exists
    const postsDir = path.join(__dirname, '..', 'data', 'posts');
    const userPostsDir = path.join(postsDir, 'db', userId);
    const postFilePath = path.join(userPostsDir, `${postId}.json`);

    if (fs.existsSync(postFilePath)) {
      try {
        fs.unlinkSync(postFilePath);
        console.log(`Deleted post JSON file: ${postFilePath}`);
      } catch (err) {
        console.error(`Error deleting post JSON file: ${postFilePath}`, err);
      }
    }

    // Delete the post-specific image files if they exist
    const postImagePath = path.join(imagesDir, `post_${postId}.jpeg`);
    const postImagePathPng = path.join(imagesDir, `post_${postId}.png`);

    if (fs.existsSync(postImagePath)) {
      try {
        fs.unlinkSync(postImagePath);
        console.log(`Deleted post-specific image file: ${postImagePath}`);
      } catch (err) {
        console.error(`Error deleting post-specific image file: ${postImagePath}`, err);
      }
    }

    if (fs.existsSync(postImagePathPng)) {
      try {
        fs.unlinkSync(postImagePathPng);
        console.log(`Deleted post-specific PNG image file: ${postImagePathPng}`);
      } catch (err) {
        console.error(`Error deleting post-specific PNG image file: ${postImagePathPng}`, err);
      }
    }

    // Delete the original image file if it exists
    if (post.image_url) {
      const imageId = post.image_url.split('/').pop();

      // Find the image file
      const files = fs.readdirSync(imagesDir);
      const imageFile = files.find(file => file.startsWith(imageId));

      if (imageFile) {
        const filePath = path.join(imagesDir, imageFile);
        try {
          fs.unlinkSync(filePath);
          console.log(`Deleted original image file: ${filePath}`);
        } catch (err) {
          console.error(`Error deleting original image file: ${filePath}`, err);
        }
      }
    }

    res.json({ success: true, message: 'Post deleted successfully' });
  } catch (error) {
    console.error('Delete post error:', error);
    res.status(500).json({ error: 'Failed to delete post' });
  }
});

// Serve images endpoint
app.get('/api/images/:imageId', (req, res) => {
  try {
    const imageId = req.params.imageId;

    // Validate imageId to prevent directory traversal attacks
    if (!imageId || imageId.includes('/') || imageId.includes('\\')) {
      return res.status(400).json({ error: 'Invalid image ID' });
    }

    // Find the image file
    const files = fs.readdirSync(imagesDir);
    const imageFile = files.find(file => file.startsWith(imageId));

    if (!imageFile) {
      return res.status(404).json({ error: 'Image not found' });
    }

    const filePath = path.join(imagesDir, imageFile);

    // Determine content type based on file extension
    const ext = path.extname(imageFile).toLowerCase();
    let contentType = 'image/jpeg'; // Default

    if (ext === '.png') contentType = 'image/png';
    else if (ext === '.gif') contentType = 'image/gif';
    else if (ext === '.webp') contentType = 'image/webp';

    // Set appropriate headers and send the file
    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year

    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('Error serving image:', error);
    res.status(500).json({ error: 'Failed to serve image' });
  }
});

// Health check endpoint
app.get('/api/health', (_req, res) => {
  try {
    const dbConnected = testConnection();
    res.json({
      status: 'ok',
      database: dbConnected ? 'connected' : 'disconnected',
      type: 'sqlite'
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      database: 'disconnected',
      error: error.message
    });
  }
});

// PayPal Webhook endpoint
app.post('/api/webhooks/paypal', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    console.log('PayPal webhook received');
    console.log('Headers:', req.headers);

    // Verify webhook signature (basic validation for now)
    const isValid = verifyWebhookSignature(req.headers, req.body, process.env.PAYPAL_WEBHOOK_ID);

    if (!isValid) {
      console.error('Invalid webhook signature');
      return res.status(401).json({ error: 'Invalid signature' });
    }

    // Parse the webhook body
    let eventData;
    try {
      eventData = JSON.parse(req.body);
    } catch (parseError) {
      console.error('Failed to parse webhook body:', parseError);
      return res.status(400).json({ error: 'Invalid JSON' });
    }

    console.log('Webhook event type:', eventData.event_type);
    console.log('Webhook event data:', JSON.stringify(eventData, null, 2));

    // Handle the webhook event
    const result = await handleWebhookEvent(eventData.event_type, eventData);

    if (result.success) {
      console.log('Webhook processed successfully:', result.message);
      res.json({ success: true, message: result.message });
    } else {
      console.error('Webhook processing failed:', result.error);
      res.status(500).json({ error: result.error });
    }

  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Manual subscription sync endpoint (admin only)
app.post('/api/admin/sync-subscriptions', authenticateAdmin, async (req, res) => {
  try {
    console.log('Manual subscription sync triggered by admin');
    const result = await runSubscriptionMaintenance();

    res.json({
      success: true,
      message: 'Subscription sync completed',
      results: result
    });
  } catch (error) {
    console.error('Manual subscription sync error:', error);
    res.status(500).json({
      error: 'Subscription sync failed',
      details: error.message
    });
  }
});

// Start server
// Initialize database
console.log('Initializing database...');
const dbInitialized = initDatabase();

if (!dbInitialized) {
  console.warn('Failed to initialize database. Server will start in fallback mode.');
  console.warn('API endpoints requiring database access will return mock data.');

  // Add middleware to handle database-dependent routes in fallback mode
  app.use(['/api/auth', '/api/profile', '/api/posts'], (req, res, next) => {
    // For GET requests, return mock data
    if (req.method === 'GET') {
      if (req.path.includes('/profile')) {
        return res.json({
          id: 'mock-user-id',
          full_name: 'Demo User',
          plan: 'free',
          posts_count: 0,
          posts_limit: 3,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_post_time: null
        });
      }

      if (req.path.includes('/posts')) {
        return res.json([]);
      }
    }

    // For POST/PATCH/DELETE requests, simulate success
    if (['POST', 'PATCH', 'DELETE'].includes(req.method)) {
      return res.json({ success: true, message: 'Operation simulated in fallback mode' });
    }

    next();
  });
} else {
  console.log('Database initialized successfully');
}

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API available at http://localhost:${PORT}/api/health`);
  console.log(`Using SQLite database in: ${process.cwd()}/data/socialspark.db`);

  // Start scheduled subscription maintenance (runs every hour)
  if (dbInitialized) {
    console.log('Starting subscription maintenance scheduler...');

    // Run immediately on startup
    setTimeout(async () => {
      try {
        console.log('Running initial subscription maintenance...');
        await runSubscriptionMaintenance();
        console.log('Initial subscription maintenance completed');
      } catch (error) {
        console.error('Initial subscription maintenance failed:', error);
      }
    }, 30000); // Wait 30 seconds after startup

    // Then run every hour
    setInterval(async () => {
      try {
        console.log('Running scheduled subscription maintenance...');
        await runSubscriptionMaintenance();
        console.log('Scheduled subscription maintenance completed');
      } catch (error) {
        console.error('Scheduled subscription maintenance failed:', error);
      }
    }, 60 * 60 * 1000); // Every hour

    console.log('Subscription maintenance scheduler started');
  } else {
    console.log('Database not initialized - skipping subscription maintenance scheduler');
  }
});
