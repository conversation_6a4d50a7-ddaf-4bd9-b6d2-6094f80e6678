import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 10, // Adjust based on your server resources (1GB RAM, 1 CPU)
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 0,
  // Add authentication options
  authPlugins: {
    mysql_native_password: () => () => Buffer.from([0]),
    mysql_clear_password: () => () => Buffer.from([]),
    sha256_password: () => () => Buffer.from([]),
    caching_sha2_password: () => () => Buffer.from([]),
    auth_gssapi_client: () => () => Buffer.from([])
  }
});

// Test the connection
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('MySQL connection established successfully');
    connection.release();
    return true;
  } catch (error) {
    console.error('Error connecting to MySQL:', error);
    return false;
  }
};

// Initialize database (create database and tables if they don't exist)
const initDatabase = async () => {
  try {
    // First, try to connect to MySQL without specifying a database
    const tempPool = mysql.createPool({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      waitForConnections: true,
      connectionLimit: 1
    });

    // Create the database if it doesn't exist
    const tempConnection = await tempPool.getConnection();
    await tempConnection.query(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME}`);
    tempConnection.release();
    await tempPool.end();

    console.log(`Ensured database ${process.env.DB_NAME} exists`);

    // Now connect to the specific database and create tables
    const connection = await pool.getConnection();

    // Create profiles table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS profiles (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        full_name VARCHAR(255),
        plan ENUM('free', 'basic', 'pro', 'ultra') DEFAULT 'free',
        posts_count INT DEFAULT 0,
        posts_limit INT DEFAULT 5
      )
    `);

    // Create posts table
    await connection.query(`
      CREATE TABLE IF NOT EXISTS posts (
        id VARCHAR(36) PRIMARY KEY,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        user_id VARCHAR(36) NOT NULL,
        content TEXT NOT NULL,
        caption TEXT,
        image_url TEXT,
        published BOOLEAN DEFAULT FALSE,
        FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE
      )
    `);

    // Create users table for basic authentication
    await connection.query(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(36) PRIMARY KEY,
        email VARCHAR(255) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (id) REFERENCES profiles(id) ON DELETE CASCADE
      )
    `);

    // Create indexes for better performance
    try {
      await connection.query(`CREATE INDEX idx_posts_user_id ON posts(user_id)`);
    } catch (err) {
      // Index might already exist, which is fine
      console.log('Note: posts index may already exist');
    }

    try {
      await connection.query(`CREATE INDEX idx_users_email ON users(email)`);
    } catch (err) {
      // Index might already exist, which is fine
      console.log('Note: users index may already exist');
    }

    console.log('Database tables initialized successfully');
    connection.release();
    return true;
  } catch (error) {
    console.error('Error initializing database:', error);
    return false;
  }
};

export { pool, testConnection, initDatabase };
