import { pool } from './db.js';
import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET;
const TOKEN_EXPIRY = '7d'; // Token expires in 7 days

// Hash a password
const hashPassword = (password) => {
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');
  return `${salt}:${hash}`;
};

// Verify a password against a hash
const verifyPassword = (password, hashedPassword) => {
  const [salt, hash] = hashedPassword.split(':');
  const calculatedHash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');
  return hash === calculatedHash;
};

// Generate a JWT token
const generateToken = (userId) => {
  return jwt.sign({ sub: userId }, JWT_SECRET, { expiresIn: TOKEN_EXPIRY });
};

// Verify a JWT token
const verifyToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
};

// Register a new user
const registerUser = async (email, password, fullName) => {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();

    // Check if user already exists
    const [existingUsers] = await connection.query(
      'SELECT * FROM users WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      await connection.rollback();
      return { error: { message: 'User already exists' } };
    }

    // Generate a new UUID for the user
    const userId = uuidv4();
    const hashedPassword = hashPassword(password);

    // Create profile first (parent table)
    await connection.query(
      'INSERT INTO profiles (id, full_name, plan, posts_count, posts_limit, last_post_time) VALUES (?, ?, ?, ?, ?, ?)',
      [userId, fullName, 'free', 0, 3, null]
    );

    // Create user (child table)
    await connection.query(
      'INSERT INTO users (id, email, password_hash) VALUES (?, ?, ?)',
      [userId, email, hashedPassword]
    );

    await connection.commit();

    // Generate token
    const token = generateToken(userId);

    return {
      data: {
        user: { id: userId, email },
        token
      },
      error: null
    };
  } catch (error) {
    await connection.rollback();
    console.error('Error registering user:', error);
    return { error: { message: 'Registration failed' } };
  } finally {
    connection.release();
  }
};

// Login a user
const loginUser = async (email, password) => {
  try {
    // Get user by email
    const [users] = await pool.query(
      'SELECT u.*, p.full_name, p.plan, p.posts_count, p.posts_limit FROM users u JOIN profiles p ON u.id = p.id WHERE u.email = ?',
      [email]
    );

    if (users.length === 0) {
      return { error: { message: 'Invalid email or password' } };
    }

    const user = users[0];

    // Verify password
    if (!verifyPassword(password, user.password_hash)) {
      return { error: { message: 'Invalid email or password' } };
    }

    // Generate token
    const token = generateToken(user.id);

    // Return user data and token
    return {
      data: {
        user: {
          id: user.id,
          email: user.email,
          profile: {
            id: user.id,
            full_name: user.full_name,
            plan: user.plan,
            posts_count: user.posts_count,
            posts_limit: user.posts_limit
          }
        },
        token
      },
      error: null
    };
  } catch (error) {
    console.error('Error logging in:', error);
    return { error: { message: 'Login failed' } };
  }
};

// Get user profile by ID
const getUserProfile = async (userId) => {
  try {
    const [profiles] = await pool.query(
      'SELECT * FROM profiles WHERE id = ?',
      [userId]
    );

    if (profiles.length === 0) {
      return { error: { message: 'Profile not found' } };
    }

    return { data: profiles[0], error: null };
  } catch (error) {
    console.error('Error getting user profile:', error);
    return { error: { message: 'Failed to get profile' } };
  }
};

// Update user profile
const updateUserProfile = async (userId, updates) => {
  try {
    const allowedFields = ['full_name', 'plan', 'posts_count', 'posts_limit'];
    const fields = Object.keys(updates).filter(key => allowedFields.includes(key));

    if (fields.length === 0) {
      return { error: { message: 'No valid fields to update' } };
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const values = fields.map(field => updates[field]);
    values.push(userId);

    const [result] = await pool.query(
      `UPDATE profiles SET ${setClause} WHERE id = ?`,
      values
    );

    if (result.affectedRows === 0) {
      return { error: { message: 'Profile not found' } };
    }

    // Get updated profile
    return await getUserProfile(userId);
  } catch (error) {
    console.error('Error updating user profile:', error);
    return { error: { message: 'Failed to update profile' } };
  }
};

export {
  registerUser,
  loginUser,
  getUserProfile,
  updateUserProfile,
  verifyToken,
  generateToken
};
